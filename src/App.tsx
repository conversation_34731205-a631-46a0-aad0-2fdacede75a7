import React, { useState } from 'react';
import { ShellProvider, MicrofrontendContainer, MicrofrontendConfig } from '@re-shell/core';

// Sample microfrontend configurations
const SAMPLE_MICROFRONTENDS: MicrofrontendConfig[] = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    url: 'http://localhost:3001/mf.js',
    containerId: 'dashboard-container',
    team: 'Team Alpha'
  },
  {
    id: 'users',
    name: 'User Management',
    url: 'http://localhost:3002/mf.js',
    containerId: 'users-container',
    team: 'Team Beta'
  }
];

function App(): React.ReactElement {
  const [microfrontends] = useState<MicrofrontendConfig[]>(SAMPLE_MICROFRONTENDS);

  return (
    <ShellProvider initialMicrofrontends={microfrontends}>
      <div className="app">
        <h1>ReShell Demo</h1>
        <p>A lightweight microfrontend shell for React</p>

        <div className="microfrontends">
          <h2>Microfrontends</h2>

          {microfrontends.map((config: MicrofrontendConfig) => (
            <div key={config.id} className="microfrontend-container">
              <h3>{config.name}</h3>
              <p>Team: {config.team}</p>

              {/* This would normally load the microfrontend,
                  but we're using mocks for the demo */}
              <MicrofrontendContainer
                config={config}
                className="microfrontend"
              />

              {/* Mock content since we don't have actual microfrontends running */}
              <div style={{ marginTop: '1rem', padding: '1rem', backgroundColor: '#f0f0f0', borderRadius: '4px' }}>
                <em>Mock {config.name} Content</em>
                <p>Normally, this content would be loaded from: {config.url}</p>
                <p>Container ID: {config.containerId}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </ShellProvider>
  );
}

export default App;