declare module '@re-shell/core' {
  import React from 'react';

  export interface MicrofrontendConfig {
    id: string;
    name: string;
    url: string;
    containerId: string;
    team: string;
  }

  export interface ShellProviderProps {
    initialMicrofrontends: MicrofrontendConfig[];
    children: React.ReactNode;
  }

  export interface MicrofrontendContainerProps {
    config: MicrofrontendConfig;
    className?: string;
  }

  export const ShellProvider: React.FC<ShellProviderProps>;
  export const MicrofrontendContainer: React.FC<MicrofrontendContainerProps>;
}