#!/bin/bash

# Package manager switcher script for Re-Shell

# Check if a package manager was specified
if [ $# -eq 0 ]; then
  echo "Usage: ./manager.sh [pnpm|npm|yarn|bun]"
  exit 1
fi

MANAGER=$1
VALID_MANAGERS=("pnpm" "npm" "yarn" "bun")

# Validate the package manager
if [[ ! " ${VALID_MANAGERS[@]} " =~ " ${MANAGER} " ]]; then
  echo "Error: Invalid package manager. Please specify one of: pnpm, npm, yarn, bun"
  exit 1
fi

echo "Switching to $MANAGER..."

# Remove lock files
echo "Removing lock files..."
rm -f yarn.lock package-lock.json pnpm-lock.yaml bun.lock

# Update scripts in package.json based on manager
echo "Updating package.json..."

if [ "$MANAGER" == "pnpm" ]; then
  # pnpm is the default, so no changes needed
  echo "Using pnpm configuration"
elif [ "$MANAGER" == "npm" ]; then
  echo "Updating for npm compatibility"
elif [ "$MANAGER" == "yarn" ]; then
  echo "Updating for yarn compatibility"
elif [ "$MANAGER" == "bun" ]; then
  echo "Updating for bun compatibility"
  # Create .bunfig.toml if it doesn't exist
  if [ ! -f ".bunfig.toml" ]; then
    echo 'logLevel = "debug"' > .bunfig.toml
    echo "Created .bunfig.toml"
  fi
fi

echo "Running install with $MANAGER..."

# Install dependencies with the selected package manager
if [ "$MANAGER" == "pnpm" ]; then
  pnpm install
elif [ "$MANAGER" == "npm" ]; then
  npm install
elif [ "$MANAGER" == "yarn" ]; then
  yarn install
elif [ "$MANAGER" == "bun" ]; then
  bun install
fi

echo "Package manager switched to $MANAGER"
echo "Run '$MANAGER install' to install dependencies"