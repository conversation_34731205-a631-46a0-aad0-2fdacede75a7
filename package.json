{"name": "re-shell-monorepo", "version": "0.2.2", "private": true, "description": "Monorepo for ReShell microfrontend framework", "scripts": {"build": "pnpm -r run build", "dev": "pnpm -r --parallel run dev", "lint": "pnpm -r run lint", "test": "pnpm -r run test", "clean": "pnpm -r run clean", "build:npm": "npm run build --workspaces --if-present", "dev:npm": "npm run dev --workspaces --if-present --parallel", "build:yarn": "yarn workspaces run build", "dev:yarn": "yarn workspaces foreach -p run dev"}, "workspaces": ["packages/*"], "engines": {"node": ">=16.0.0"}, "keywords": ["microfrontend", "react", "shell", "framework", "monorepo"], "author": "Re-Shell Organization", "license": "MIT", "dependencies": {"@re-shell/core": "file:/Users/<USER>/Projects/Re-Shell/reshell-monorepo/packages/core/re-shell-core-0.3.2.tgz", "@re-shell/ui": "file:/Users/<USER>/Projects/Re-Shell/reshell-monorepo/packages/ui/re-shell-ui-0.2.2.tgz", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.0", "eslint-plugin-react": "^7.0.0", "eslint-plugin-react-hooks": "^4.0.0", "prettier": "^2.0.0", "typescript": "^5.0.0"}}