# Re-Shell Monorepo

A lightweight microfrontend shell for React applications.

## Overview

Re-Shell is a framework that enables multiple teams to work on separate microfrontends while combining them into a cohesive application. This monorepo contains the core framework, CLI tools, and example microfrontends.

## Repository Structure

```
re-shell/
├── packages/             # Shared libraries
│   ├── core/             # Core framework (v0.3.2)
│   ├── cli/              # CLI tool (v0.2.8)
│   └── ui/               # Shared UI components (v0.2.2)
├── docs/                 # Documentation
└── scripts/              # Build and development scripts
```

## Features

### Core Framework (@re-shell/core v0.3.2)
- **Multi-framework Support**: React, Vue, Svelte, Angular, and Vanilla JS
- **Enhanced Event Bus**: Type-safe inter-microfrontend communication with namespacing
- **Advanced Routing**: Route-based loading with guards and dynamic parameters
- **Performance Monitoring**: Built-in metrics collection and analysis
- **Development Tools**: Debug panel, HMR support, and comprehensive debugging

### CLI Tool (@re-shell/cli v0.2.8)
- **Project Scaffolding**: Quick setup of microfrontend projects and workspaces
- **Multiple Templates**: React, Vue, Svelte templates with best practices
- **Workspace Management**: Monorepo support with dependency analysis
- **Build Orchestration**: Coordinated builds across multiple microfrontends
- **Development Server**: Integrated development environment with hot reloading

### UI Components (@re-shell/ui v0.2.2)
- **22+ Production-Ready Components**: From basic buttons to complex data displays
- **Accessibility First**: WCAG 2.1 AA compliant with comprehensive screen reader support
- **Microfrontend Optimized**: Cross-MF communication, error boundaries, and shared state
- **Module Federation**: Built-in Webpack Module Federation support
- **TypeScript**: Full type safety with comprehensive interfaces

## Getting Started

### Prerequisites

- Node.js (v16 or later)
- A package manager: pnpm (recommended), npm, yarn, or bun

### Package Manager Compatibility

This monorepo supports multiple package managers:

- pnpm (primary, recommended)
- npm
- yarn
- bun

For detailed instructions on using different package managers, see [PACKAGE_MANAGERS.md](./PACKAGE_MANAGERS.md).

### Installation

```bash
# Clone the repository
git clone https://github.com/Re-Shell/re-shell.git
cd re-shell

# Install dependencies (with pnpm)
pnpm install

# Build all packages
pnpm build

# Start development servers
pnpm dev
```

## Workspace Commands

- `pnpm build` - Build all packages and applications
- `pnpm dev` - Start development servers for all applications
- `pnpm lint` - Run linting on all packages
- `pnpm test` - Run tests for all packages
- `pnpm clean` - Clean build artifacts

## Creating a New Microfrontend

### Using the Global CLI

```bash
# Install the CLI globally
npm install -g @re-shell/cli

# Create a new microfrontend
re-shell add my-microfrontend --team MyTeam
```

### Using the Local CLI

```bash
# Using the CLI directly from the monorepo (after building)
node packages/cli/dist/index.js add my-microfrontend --team MyTeam
```

The CLI supports various options:

```bash
re-shell add my-feature --team "My Team" --template react-ts --route /my-feature
```

For more information about the CLI, see the [CLI documentation](./packages/cli/README.md).

## CLI Commands

The Re-Shell CLI (v0.2.2) supports the following commands:

- `add` - Add a new microfrontend to your project
- `remove` - Remove a microfrontend from your project
- `list` - List all registered microfrontends
- `build` - Build one or all microfrontends
- `serve` - Serve microfrontends locally for development

## Documentation

For more detailed documentation, please refer to:

- [Integration Guide](./INTEGRATION.md) - How to integrate microfrontends
- [Architecture](./docs/architecture.md) - System architecture overview
- [Project Plan](./docs/PROJECT_PLAN.md) - Development roadmap
- [Requirements](./docs/requirements.md) - System requirements
- [Commit Convention](./docs/COMMIT_CONVENTION.md) - Contribution guidelines
- [Package Manager Guide](./PACKAGE_MANAGERS.md) - Using different package managers

## License

MIT License