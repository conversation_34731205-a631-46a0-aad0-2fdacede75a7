# Changelog

All notable changes to the `@re-shell/ui` package will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.2.2] - 2024-01-20

### Added
- **Microfrontend-specific components**: NavigationShell, MicrofrontendContainer, SharedStateProvider
- **Module Federation support**: Complete Webpack Module Federation integration utilities
- **Feature flags system**: A/B testing and feature toggling capabilities
- **Error boundaries**: Robust error handling and fallback UI components
- **Enhanced accessibility**: WCAG 2.1 AA compliance across all components
- **Advanced form components**: Validation, helper text, and enhanced styling options

### Fixed
- Version consistency across package.json and source files
- TypeScript type definitions for all exported components
- Accessibility improvements for screen readers and keyboard navigation

### Changed
- Improved documentation with comprehensive examples and usage patterns
- Enhanced component architecture following atomic design principles
- Updated styling system with better dark mode support

## [0.2.1] - 2023-12-15

### Fixed
- Build configuration issues
- TypeScript declaration file generation
- Component export consistency

## [0.2.0] - 2023-09-20

### Added
- New Button component with enhanced styling options
- Added comprehensive test suite with React Testing Library
- Theme integration for consistent styling
- Accessibility improvements across all components
- Added support for dark mode

### Changed
- Improved component API for better type safety
- Enhanced styling system for better customization
- Refactored internal structure for better maintainability

### Fixed
- Fixed styling inconsistencies in various components
- Resolved accessibility issues in interactive components
- Fixed responsive layout issues

## [0.1.0] - 2023-08-15

### Added
- Initial release of UI component library
- Basic component set: Button, Card, Layout components
- Simple theming support
- TypeScript definitions