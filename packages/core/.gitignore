# Dependencies
node_modules
.pnp
.pnp.js
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# Build outputs
dist
dist-ssr
*.local
build
lib
storybook-static

# Testing
coverage
.nyc_output
test-output
test-results
test-reports
.vitest-cache

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/launch.json
!.vscode/tasks.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.history
.project
.classpath

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production
.env.*.local

# Cache
.cache
.parcel-cache
.npm
.eslintcache
.stylelintcache
.rollup.cache
.babel-cache

# AI tools
**/.claude/settings.local.json
.cursor/
.copilot/
