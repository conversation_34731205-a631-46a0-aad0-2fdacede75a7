import { DevToolsConfig, MicrofrontendConfig, Environment } from './types';
import { eventBus } from './eventBus';
import { performanceMonitor } from './performance';

/**
 * Development tools for debugging and monitoring microfrontends
 */
export class DevTools {
  private config: DevToolsConfig;
  private isEnabled = false;
  private debugPanel: HTMLElement | null = null;

  constructor(config: DevToolsConfig = { enabled: false }) {
    this.config = config;
    this.isEnabled = config.enabled && this.isDevelopment();
    
    if (this.isEnabled) {
      this.initialize();
    }
  }

  /**
   * Check if we're in development environment
   */
  private isDevelopment(): boolean {
    return typeof window !== 'undefined' && 
           (window.location.hostname === 'localhost' || 
            window.location.hostname === '127.0.0.1' ||
            window.location.hostname.includes('dev'));
  }

  /**
   * Initialize development tools
   */
  private initialize(): void {
    if (typeof window === 'undefined') return;

    // Add global debug object
    (window as any).__RE_SHELL_DEV__ = {
      eventBus,
      performanceMonitor,
      devTools: this,
      getMicrofrontends: () => this.getRegisteredMicrofrontends(),
      getEventHistory: () => eventBus.getEventHistory(),
      getPerformanceMetrics: () => performanceMonitor.getAllMetrics(),
      clearEventHistory: () => eventBus.clearHistory(),
      clearPerformanceMetrics: () => performanceMonitor.clearMetrics(),
      enableEventLogging: () => eventBus.setDebugMode(true),
      disableEventLogging: () => eventBus.setDebugMode(false),
      showDebugPanel: () => this.showDebugPanel(),
      hideDebugPanel: () => this.hideDebugPanel()
    };

    // Set up event logging if enabled
    if (this.config.logEvents) {
      eventBus.setDebugMode(true);
    }

    // Set up performance monitoring if enabled
    if (this.config.monitorPerformance) {
      performanceMonitor.updateConfig({
        enabled: true,
        logToConsole: true,
        emitEvents: true
      });
    }

    // Set up HMR if enabled
    if (this.config.enableHMR) {
      this.setupHMR();
    }

    // Register custom debug functions
    if (this.config.debugFunctions) {
      Object.entries(this.config.debugFunctions).forEach(([name, fn]) => {
        (window as any).__RE_SHELL_DEV__[name] = fn;
      });
    }

    console.log('[Re-Shell DevTools] Development tools initialized');
    console.log('Access via window.__RE_SHELL_DEV__');
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<DevToolsConfig>): void {
    this.config = { ...this.config, ...config };
    
    if (config.enabled !== undefined) {
      this.isEnabled = config.enabled && this.isDevelopment();
    }

    if (this.isEnabled && typeof window !== 'undefined') {
      this.initialize();
    }
  }

  /**
   * Set up Hot Module Replacement
   */
  private setupHMR(): void {
    if (typeof window === 'undefined') return;

    // Listen for HMR events
    eventBus.on('hmr:update', (data) => {
      console.log('[HMR] Update received:', data.payload);
      
      // Reload specific microfrontend if specified
      if (typeof data.payload === 'object' && data.payload && 'microfrontendId' in data.payload) {
        this.reloadMicrofrontend((data.payload as any).microfrontendId);
      }
    });

    // Set up WebSocket connection for HMR (if available)
    if ('WebSocket' in window) {
      try {
        const ws = new WebSocket('ws://localhost:24678'); // Common HMR port
        
        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            if (data.type === 'update') {
              eventBus.emit('hmr:update', data);
            }
          } catch (error) {
            // Ignore parsing errors
          }
        };

        ws.onerror = () => {
          // HMR WebSocket not available, that's okay
        };
      } catch (error) {
        // WebSocket connection failed, that's okay
      }
    }
  }

  /**
   * Reload a specific microfrontend
   */
  private reloadMicrofrontend(microfrontendId: string): void {
    eventBus.emit('microfrontend:reload', { microfrontendId });
    console.log(`[HMR] Reloading microfrontend: ${microfrontendId}`);
  }

  /**
   * Get registered microfrontends (placeholder - would be implemented by shell)
   */
  private getRegisteredMicrofrontends(): MicrofrontendConfig[] {
    // This would typically be provided by the shell application
    return [];
  }

  /**
   * Show debug panel
   */
  showDebugPanel(): void {
    if (typeof window === 'undefined' || this.debugPanel) return;

    this.debugPanel = document.createElement('div');
    this.debugPanel.id = 're-shell-debug-panel';
    this.debugPanel.innerHTML = this.getDebugPanelHTML();
    this.debugPanel.style.cssText = this.getDebugPanelCSS();

    document.body.appendChild(this.debugPanel);

    // Add event listeners
    this.setupDebugPanelEvents();
  }

  /**
   * Hide debug panel
   */
  hideDebugPanel(): void {
    if (this.debugPanel) {
      this.debugPanel.remove();
      this.debugPanel = null;
    }
  }

  /**
   * Get debug panel HTML
   */
  private getDebugPanelHTML(): string {
    return `
      <div class="debug-panel-header">
        <h3>Re-Shell Debug Panel</h3>
        <button class="debug-panel-close">×</button>
      </div>
      <div class="debug-panel-content">
        <div class="debug-section">
          <h4>Event Bus</h4>
          <button id="debug-clear-events">Clear Event History</button>
          <button id="debug-toggle-event-logging">Toggle Event Logging</button>
          <div id="debug-event-count">Events: 0</div>
        </div>
        <div class="debug-section">
          <h4>Performance</h4>
          <button id="debug-clear-metrics">Clear Metrics</button>
          <div id="debug-performance-summary">No metrics available</div>
        </div>
        <div class="debug-section">
          <h4>Microfrontends</h4>
          <div id="debug-microfrontends-list">No microfrontends registered</div>
        </div>
      </div>
    `;
  }

  /**
   * Get debug panel CSS
   */
  private getDebugPanelCSS(): string {
    return `
      position: fixed;
      top: 20px;
      right: 20px;
      width: 300px;
      max-height: 500px;
      background: #1a1a1a;
      color: #ffffff;
      border: 1px solid #333;
      border-radius: 8px;
      font-family: monospace;
      font-size: 12px;
      z-index: 10000;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    `;
  }

  /**
   * Set up debug panel event listeners
   */
  private setupDebugPanelEvents(): void {
    if (!this.debugPanel) return;

    // Close button
    const closeBtn = this.debugPanel.querySelector('.debug-panel-close');
    closeBtn?.addEventListener('click', () => this.hideDebugPanel());

    // Clear events button
    const clearEventsBtn = this.debugPanel.querySelector('#debug-clear-events');
    clearEventsBtn?.addEventListener('click', () => {
      eventBus.clearHistory();
      this.updateDebugPanel();
    });

    // Toggle event logging button
    const toggleLoggingBtn = this.debugPanel.querySelector('#debug-toggle-event-logging');
    toggleLoggingBtn?.addEventListener('click', () => {
      const currentMode = eventBus.getStats().totalEvents > 0;
      eventBus.setDebugMode(!currentMode);
      this.updateDebugPanel();
    });

    // Clear metrics button
    const clearMetricsBtn = this.debugPanel.querySelector('#debug-clear-metrics');
    clearMetricsBtn?.addEventListener('click', () => {
      performanceMonitor.clearMetrics();
      this.updateDebugPanel();
    });

    // Update panel content periodically
    setInterval(() => this.updateDebugPanel(), 1000);
  }

  /**
   * Update debug panel content
   */
  private updateDebugPanel(): void {
    if (!this.debugPanel) return;

    // Update event count
    const eventCount = this.debugPanel.querySelector('#debug-event-count');
    if (eventCount) {
      const stats = eventBus.getStats();
      eventCount.textContent = `Events: ${stats.historySize}`;
    }

    // Update performance summary
    const perfSummary = this.debugPanel.querySelector('#debug-performance-summary');
    if (perfSummary) {
      const summary = performanceMonitor.getSummary();
      perfSummary.textContent = `Microfrontends: ${summary.totalMicrofrontends}, Avg Load: ${summary.averageLoadTime.toFixed(2)}ms`;
    }

    // Update microfrontends list
    const mfList = this.debugPanel.querySelector('#debug-microfrontends-list');
    if (mfList) {
      const microfrontends = this.getRegisteredMicrofrontends();
      mfList.textContent = microfrontends.length > 0 
        ? microfrontends.map(mf => mf.name).join(', ')
        : 'No microfrontends registered';
    }
  }

  /**
   * Log debug information
   */
  log(message: string, data?: any): void {
    if (!this.isEnabled) return;

    console.log(`[Re-Shell Debug] ${message}`, data || '');
  }

  /**
   * Log warning
   */
  warn(message: string, data?: any): void {
    if (!this.isEnabled) return;

    console.warn(`[Re-Shell Debug] ${message}`, data || '');
  }

  /**
   * Log error
   */
  error(message: string, error?: Error): void {
    if (!this.isEnabled) return;

    console.error(`[Re-Shell Debug] ${message}`, error || '');
  }

  /**
   * Check if dev tools are enabled
   */
  isDevToolsEnabled(): boolean {
    return this.isEnabled;
  }
}

// Export singleton instance
export const devTools = new DevTools();

/**
 * Development utilities
 */
export const DevUtils = {
  /**
   * Check if running in development mode
   */
  isDevelopment(): boolean {
    return process.env.NODE_ENV === 'development' ||
           (typeof window !== 'undefined' && 
            (window.location.hostname === 'localhost' || 
             window.location.hostname === '127.0.0.1'));
  },

  /**
   * Get environment
   */
  getEnvironment(): Environment {
    if (process.env.NODE_ENV === 'test') return 'test';
    if (process.env.NODE_ENV === 'production') return 'production';
    return 'development';
  },

  /**
   * Debug function that only runs in development
   */
  debug(fn: () => void): void {
    if (this.isDevelopment()) {
      fn();
    }
  },

  /**
   * Assert function for development
   */
  assert(condition: boolean, message: string): void {
    if (this.isDevelopment() && !condition) {
      throw new Error(`[Re-Shell Assert] ${message}`);
    }
  }
};
