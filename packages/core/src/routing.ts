import { RouteConfig, RouteGuard, MicrofrontendConfig } from './types';
import { eventBus } from './eventBus';

/**
 * Route matching result
 */
export interface RouteMatch {
  route: RouteConfig;
  params: Record<string, string>;
  query: Record<string, string>;
}

/**
 * Navigation options
 */
export interface NavigationOptions {
  replace?: boolean;
  state?: any;
  skipGuards?: boolean;
}

/**
 * Router class for managing microfrontend routes
 */
export class Router {
  private routes: RouteConfig[] = [];
  private currentRoute: RouteConfig | null = null;
  private microfrontends: Map<string, MicrofrontendConfig> = new Map();
  private isNavigating = false;

  constructor() {
    // Listen for browser navigation events
    if (typeof window !== 'undefined') {
      window.addEventListener('popstate', this.handlePopState.bind(this));
    }
  }

  /**
   * Register routes
   */
  addRoutes(routes: RouteConfig[]): void {
    this.routes.push(...routes);
    
    // Sort routes by specificity (exact matches first, then by path length)
    this.routes.sort((a, b) => {
      if (a.exact && !b.exact) return -1;
      if (!a.exact && b.exact) return 1;
      return b.path.length - a.path.length;
    });
  }

  /**
   * Register a single route
   */
  addRoute(route: RouteConfig): void {
    this.addRoutes([route]);
  }

  /**
   * Register microfrontends for routing
   */
  registerMicrofrontend(config: MicrofrontendConfig): void {
    this.microfrontends.set(config.id, config);
  }

  /**
   * Navigate to a path
   */
  async navigate(path: string, options: NavigationOptions = {}): Promise<boolean> {
    if (this.isNavigating) {
      return false;
    }

    this.isNavigating = true;

    try {
      const match = this.matchRoute(path);
      
      if (!match) {
        console.warn(`No route found for path: ${path}`);
        this.isNavigating = false;
        return false;
      }

      // Run route guards
      if (!options.skipGuards && match.route.guards) {
        const canNavigate = await this.runGuards(match.route.guards, match.route, this.currentRoute);
        if (!canNavigate) {
          this.isNavigating = false;
          return false;
        }
      }

      // Update browser history
      if (typeof window !== 'undefined') {
        if (options.replace) {
          window.history.replaceState(options.state, '', path);
        } else {
          window.history.pushState(options.state, '', path);
        }
      }

      // Emit navigation events
      eventBus.emit('route:before-change', {
        from: this.currentRoute,
        to: match.route,
        path,
        params: match.params,
        query: match.query
      });

      // Update current route
      const previousRoute = this.currentRoute;
      this.currentRoute = match.route;

      // Emit navigation success event
      eventBus.emit('route:changed', {
        from: previousRoute,
        to: match.route,
        path,
        params: match.params,
        query: match.query
      });

      this.isNavigating = false;
      return true;
    } catch (error) {
      console.error('Navigation error:', error);
      this.isNavigating = false;
      return false;
    }
  }

  /**
   * Get current route
   */
  getCurrentRoute(): RouteConfig | null {
    return this.currentRoute;
  }

  /**
   * Get current path
   */
  getCurrentPath(): string {
    if (typeof window !== 'undefined') {
      return window.location.pathname;
    }
    return '/';
  }

  /**
   * Match a path against registered routes
   */
  matchRoute(path: string): RouteMatch | null {
    const [pathname, search] = path.split('?');
    const query = this.parseQuery(search || '');

    for (const route of this.routes) {
      const match = this.matchPath(pathname, route.path, route.exact);
      if (match) {
        return {
          route,
          params: match,
          query
        };
      }
    }

    return null;
  }

  /**
   * Match a path pattern
   */
  private matchPath(pathname: string, pattern: string, exact = false): Record<string, string> | null {
    const patternParts = pattern.split('/').filter(Boolean);
    const pathParts = pathname.split('/').filter(Boolean);

    if (exact && patternParts.length !== pathParts.length) {
      return null;
    }

    if (!exact && pathParts.length < patternParts.length) {
      return null;
    }

    const params: Record<string, string> = {};

    for (let i = 0; i < patternParts.length; i++) {
      const patternPart = patternParts[i];
      const pathPart = pathParts[i];

      if (patternPart.startsWith(':')) {
        // Dynamic parameter
        const paramName = patternPart.slice(1);
        params[paramName] = pathPart || '';
      } else if (patternPart !== pathPart) {
        return null;
      }
    }

    return params;
  }

  /**
   * Parse query string
   */
  private parseQuery(search: string): Record<string, string> {
    const query: Record<string, string> = {};
    
    if (!search) return query;

    const params = new URLSearchParams(search);
    for (const [key, value] of params.entries()) {
      query[key] = value;
    }

    return query;
  }

  /**
   * Run route guards
   */
  private async runGuards(
    guards: RouteGuard[], 
    to: RouteConfig, 
    from: RouteConfig | null
  ): Promise<boolean> {
    for (const guard of guards) {
      try {
        const result = await guard(to, from || undefined);
        if (!result) {
          return false;
        }
      } catch (error) {
        console.error('Route guard error:', error);
        return false;
      }
    }
    return true;
  }

  /**
   * Handle browser back/forward navigation
   */
  private handlePopState(): void {
    const path = this.getCurrentPath();
    this.navigate(path, { skipGuards: false, replace: true });
  }

  /**
   * Get all registered routes
   */
  getRoutes(): RouteConfig[] {
    return [...this.routes];
  }

  /**
   * Clear all routes
   */
  clearRoutes(): void {
    this.routes = [];
    this.currentRoute = null;
  }

  /**
   * Generate URL for a route
   */
  generateUrl(routePath: string, params: Record<string, string> = {}, query: Record<string, string> = {}): string {
    let url = routePath;

    // Replace parameters
    for (const [key, value] of Object.entries(params)) {
      url = url.replace(`:${key}`, value);
    }

    // Add query string
    const queryString = new URLSearchParams(query).toString();
    if (queryString) {
      url += `?${queryString}`;
    }

    return url;
  }

  /**
   * Check if a path matches the current route
   */
  isActive(path: string, exact = false): boolean {
    const currentPath = this.getCurrentPath();
    
    if (exact) {
      return currentPath === path;
    }
    
    return currentPath.startsWith(path);
  }
}

// Export singleton instance
export const router = new Router();

/**
 * Route utilities
 */
export const RouteUtils = {
  /**
   * Create a route guard that checks authentication
   */
  createAuthGuard(isAuthenticated: () => boolean | Promise<boolean>): RouteGuard {
    return async () => {
      const authenticated = await isAuthenticated();
      if (!authenticated) {
        console.warn('Navigation blocked: User not authenticated');
        return false;
      }
      return true;
    };
  },

  /**
   * Create a route guard that checks permissions
   */
  createPermissionGuard(hasPermission: (route: RouteConfig) => boolean | Promise<boolean>): RouteGuard {
    return async (to) => {
      const permitted = await hasPermission(to);
      if (!permitted) {
        console.warn('Navigation blocked: Insufficient permissions');
        return false;
      }
      return true;
    };
  },

  /**
   * Create a route guard that confirms navigation
   */
  createConfirmGuard(message: string): RouteGuard {
    return async () => {
      if (typeof window !== 'undefined') {
        return window.confirm(message);
      }
      return true;
    };
  }
};
