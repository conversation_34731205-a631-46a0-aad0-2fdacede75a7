import {
  MicrofrontendConfig,
  LoadMicrofrontendOptions,
  MicrofrontendLifecycleHooks,
  MicrofrontendLifecycleState,
} from './types';
import { eventBus } from './eventBus';
import { performanceMonitor } from './performance';
import { frameworkRegistry } from './framework';

/**
 * Microfrontend instance tracking
 */
interface MicrofrontendInstance {
  config: MicrofrontendConfig;
  state: MicrofrontendLifecycleState;
  container: HTMLElement;
  script?: HTMLScriptElement;
  loadedAt?: number;
  mountedAt?: number;
}

/**
 * Global registry of loaded microfrontends
 */
const microfrontendRegistry = new Map<string, MicrofrontendInstance>();

/**
 * Enhanced microfrontend loader with lifecycle management
 *
 * @param config - Configuration for the microfrontend
 * @param options - Options for loading the microfrontend
 * @param hooks - Lifecycle hooks
 * @returns Promise that resolves when the microfrontend is loaded
 */
export const loadMicrofrontend = async (
  config: MicrofrontendConfig,
  options: LoadMicrofrontendOptions = {},
  hooks: MicrofrontendLifecycleHooks = {}
): Promise<void> => {
  const { containerId, url, id, framework } = config;
  const {
    async = true,
    onLoad,
    onError,
    timeout = 30000,
    enablePerformanceMonitoring = false,
    loadingStrategy = 'eager'
  } = options;

  // Check if already loaded
  if (microfrontendRegistry.has(id)) {
    const instance = microfrontendRegistry.get(id);
    if (instance && (instance.state === 'loaded' || instance.state === 'mounted')) {
      return Promise.resolve();
    }
  }

  // Start performance monitoring
  if (enablePerformanceMonitoring) {
    performanceMonitor.startLoad(id);
  }

  // Emit loading event
  eventBus.emit('microfrontend:loading', { config, options }, { source: 'loader' });

  return new Promise<void>((resolve, reject) => {
    const loadAsync = async () => {
      let timeoutId: number | undefined;
      let container: HTMLElement | null = null;

      try {
        // Run beforeMount hook
        if (hooks.beforeMount) {
          await hooks.beforeMount(config);
        }

      // Setup timeout handler
      if (timeout > 0) {
        timeoutId = setTimeout(() => {
          updateMicrofrontendState(id, 'error');
          const error = new Error(
            `Timeout of ${timeout}ms exceeded when loading microfrontend from "${url}"`
          );
          handleError(error);
        }, timeout) as unknown as number;
      }

      // Find container element
      container = document.getElementById(containerId);
      if (!container) {
        throw new Error(`Container element with ID "${containerId}" not found`);
      }

      // Create microfrontend instance
      const instance: MicrofrontendInstance = {
        config,
        state: 'loading',
        container,
      };
      microfrontendRegistry.set(id, instance);

      // Define loadScript function
      const loadScript = () => {
        const script = document.createElement('script');
        script.src = url;
        script.async = async;

        // Update instance with script reference
        instance.script = script;

        script.onload = async () => {
          try {
            if (timeoutId) clearTimeout(timeoutId);

            // End performance monitoring for load
            if (enablePerformanceMonitoring) {
              performanceMonitor.endLoad(id);
              performanceMonitor.startMount(id);
            }

            // Update state
            updateMicrofrontendState(id, 'loaded');
            instance.loadedAt = Date.now();

            // Get framework adapter and mount
            const adapter = frameworkRegistry.get(framework || 'vanilla');
            if (adapter && container) {
              await adapter.mount(config, container);
              updateMicrofrontendState(id, 'mounted');
              instance.mountedAt = Date.now();

              // End performance monitoring for mount
              if (enablePerformanceMonitoring) {
                performanceMonitor.endMount(id);
              }
            }

            // Run afterMount hook
            if (hooks.afterMount) {
              await hooks.afterMount(config);
            }

            // Emit loaded event
            eventBus.emit('microfrontend:loaded', { config }, { source: 'loader' });

            if (onLoad) onLoad();
            resolve();
          } catch (error) {
            handleError(error as Error);
          }
        };

        script.onerror = () => {
          const error = new Error(`Failed to load microfrontend from "${url}"`);
          handleError(error);
        };

        // Load stylesheets if specified
        if (config.stylesheets) {
          config.stylesheets.forEach(stylesheet => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = stylesheet;
            document.head.appendChild(link);
          });
        }

        // Append script to head (not container)
        document.head.appendChild(script);
      };

      // Handle different loading strategies
      if (loadingStrategy === 'lazy') {
        // Implement intersection observer for lazy loading
        const observer = new IntersectionObserver(entries => {
          if (entries[0].isIntersecting) {
            observer.disconnect();
            loadScript();
          }
        });
        observer.observe(container);
      } else if (loadingStrategy === 'preload') {
        // Preload the script but don't execute
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = url;
        link.as = 'script';
        document.head.appendChild(link);
        // Then load normally
        loadScript();
      } else {
        // Eager loading (default)
        loadScript();
      }

      const handleError = (error: Error) => {
        if (timeoutId) clearTimeout(timeoutId);
        updateMicrofrontendState(id, 'error');

        // Run error hook
        if (hooks.onError) {
          hooks.onError(error, config);
        }

        // Emit error event
        eventBus.emit(
          'microfrontend:error',
          { config, error: error.message },
          { source: 'loader' }
        );

        if (onError) onError(error);
        reject(error);
      };
    } catch (error) {
      if (timeoutId) clearTimeout(timeoutId);
      updateMicrofrontendState(id, 'error');

      // Run error hook
      if (hooks.onError) {
        hooks.onError(error as Error, config);
      }

      // Emit error event
      eventBus.emit(
        'microfrontend:error',
        { config, error: (error as Error).message },
        { source: 'loader' }
      );

      if (onError) onError(error as Error);
      reject(error as Error);
    }
    };

    loadAsync();
  });
};

/**
 * Update microfrontend state
 */
function updateMicrofrontendState(id: string, state: MicrofrontendLifecycleState): void {
  const instance = microfrontendRegistry.get(id);
  if (instance) {
    instance.state = state;
    eventBus.emit('microfrontend:state-changed', { id, state }, { source: 'loader' });
  }
}

/**
 * Unmount a microfrontend
 */
export const unmountMicrofrontend = async (
  id: string,
  hooks: MicrofrontendLifecycleHooks = {}
): Promise<void> => {
  const instance = microfrontendRegistry.get(id);
  if (!instance) {
    throw new Error(`Microfrontend with ID "${id}" not found`);
  }

  try {
    // Run beforeUnmount hook
    if (hooks.beforeUnmount) {
      await hooks.beforeUnmount(instance.config);
    }

    // Get framework adapter and unmount
    const adapter = frameworkRegistry.get(instance.config.framework || 'vanilla');
    if (adapter) {
      await adapter.unmount(instance.config, instance.container);
    }

    // Update state
    updateMicrofrontendState(id, 'unmounted');

    // Run afterUnmount hook
    if (hooks.afterUnmount) {
      await hooks.afterUnmount(instance.config);
    }

    // Emit unmounted event
    eventBus.emit('microfrontend:unmounted', { config: instance.config }, { source: 'loader' });

    // Remove from registry
    microfrontendRegistry.delete(id);
  } catch (error) {
    if (hooks.onError) {
      hooks.onError(error as Error, instance.config);
    }
    throw error;
  }
};

/**
 * Get microfrontend instance
 */
export const getMicrofrontendInstance = (id: string): MicrofrontendInstance | undefined => {
  return microfrontendRegistry.get(id);
};

/**
 * Get all microfrontend instances
 */
export const getAllMicrofrontendInstances = (): MicrofrontendInstance[] => {
  return Array.from(microfrontendRegistry.values());
};

/**
 * Check if microfrontend is loaded
 */
export const isMicrofrontendLoaded = (id: string): boolean => {
  const instance = microfrontendRegistry.get(id);
  return instance ? instance.state === 'loaded' || instance.state === 'mounted' : false;
};

/**
 * Loads multiple microfrontends
 *
 * @param configs - Array of microfrontend configurations
 * @param options - Options for loading the microfrontends
 * @param hooks - Lifecycle hooks
 * @returns Promise that resolves when all microfrontends are loaded
 */
export const loadMicrofrontends = async (
  configs: MicrofrontendConfig[],
  options: LoadMicrofrontendOptions = {},
  hooks: MicrofrontendLifecycleHooks = {}
): Promise<void> => {
  await Promise.all(configs.map(config => loadMicrofrontend(config, options, hooks)));
};

/**
 * Reload a microfrontend
 */
export const reloadMicrofrontend = async (
  id: string,
  options: LoadMicrofrontendOptions = {},
  hooks: MicrofrontendLifecycleHooks = {}
): Promise<void> => {
  const instance = microfrontendRegistry.get(id);
  if (!instance) {
    throw new Error(`Microfrontend with ID "${id}" not found`);
  }

  // Unmount first
  await unmountMicrofrontend(id, hooks);

  // Then reload
  await loadMicrofrontend(instance.config, options, hooks);
};

/**
 * Clear all microfrontends
 */
export const clearAllMicrofrontends = async (
  hooks: MicrofrontendLifecycleHooks = {}
): Promise<void> => {
  const instances = Array.from(microfrontendRegistry.keys());
  await Promise.all(instances.map(id => unmountMicrofrontend(id, hooks)));
};
