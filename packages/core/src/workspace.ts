import { WorkspaceConfig, WorkspaceType, FrameworkType, PackageManager } from './types';

/**
 * Workspace dependency information
 */
export interface WorkspaceDependency {
  name: string;
  version: string;
  type: 'dependency' | 'devDependency' | 'peerDependency';
  workspace?: string;
}

/**
 * Workspace dependency graph node
 */
export interface DependencyGraphNode {
  name: string;
  type: WorkspaceType;
  framework?: FrameworkType;
  dependencies: string[];
  dependents: string[];
  path: string;
}

/**
 * Workspace analysis result
 */
export interface WorkspaceAnalysis {
  totalWorkspaces: number;
  workspacesByType: Record<WorkspaceType, number>;
  workspacesByFramework: Record<FrameworkType, number>;
  dependencyGraph: Map<string, DependencyGraphNode>;
  circularDependencies: string[][];
  orphanedWorkspaces: string[];
}

/**
 * Workspace manager for handling monorepo operations
 */
export class WorkspaceManager {
  private workspaces: Map<string, WorkspaceConfig> = new Map();
  private packageManager: PackageManager = 'pnpm';

  /**
   * Set the package manager
   */
  setPackageManager(pm: PackageManager): void {
    this.packageManager = pm;
  }

  /**
   * Get the current package manager
   */
  getPackageManager(): PackageManager {
    return this.packageManager;
  }

  /**
   * Register a workspace
   */
  registerWorkspace(config: WorkspaceConfig): void {
    this.workspaces.set(config.name, config);
  }

  /**
   * Get a workspace by name
   */
  getWorkspace(name: string): WorkspaceConfig | undefined {
    return this.workspaces.get(name);
  }

  /**
   * Get all workspaces
   */
  getAllWorkspaces(): WorkspaceConfig[] {
    return Array.from(this.workspaces.values());
  }

  /**
   * Get workspaces by type
   */
  getWorkspacesByType(type: WorkspaceType): WorkspaceConfig[] {
    return this.getAllWorkspaces().filter(ws => ws.type === type);
  }

  /**
   * Get workspaces by framework
   */
  getWorkspacesByFramework(framework: FrameworkType): WorkspaceConfig[] {
    return this.getAllWorkspaces().filter(ws => ws.framework === framework);
  }

  /**
   * Analyze workspace dependencies
   */
  analyzeWorkspaces(): WorkspaceAnalysis {
    const workspaces = this.getAllWorkspaces();
    const dependencyGraph = new Map<string, DependencyGraphNode>();
    
    // Build dependency graph
    for (const workspace of workspaces) {
      const node: DependencyGraphNode = {
        name: workspace.name,
        type: workspace.type,
        framework: workspace.framework,
        dependencies: [],
        dependents: [],
        path: workspace.path
      };

      // Extract workspace dependencies
      if (workspace.dependencies) {
        for (const [depName] of Object.entries(workspace.dependencies)) {
          if (this.workspaces.has(depName)) {
            node.dependencies.push(depName);
          }
        }
      }

      dependencyGraph.set(workspace.name, node);
    }

    // Build dependents list
    for (const [name, node] of dependencyGraph.entries()) {
      for (const depName of node.dependencies) {
        const depNode = dependencyGraph.get(depName);
        if (depNode) {
          depNode.dependents.push(name);
        }
      }
    }

    // Find circular dependencies
    const circularDependencies = this.findCircularDependencies(dependencyGraph);

    // Find orphaned workspaces (no dependencies and no dependents)
    const orphanedWorkspaces = Array.from(dependencyGraph.entries())
      .filter(([, node]) => node.dependencies.length === 0 && node.dependents.length === 0)
      .map(([name]) => name);

    // Count by type and framework
    const workspacesByType: Record<WorkspaceType, number> = {
      app: 0,
      package: 0,
      lib: 0,
      tool: 0
    };

    const workspacesByFramework: Record<FrameworkType, number> = {
      react: 0,
      vue: 0,
      svelte: 0,
      vanilla: 0,
      angular: 0
    };

    for (const workspace of workspaces) {
      workspacesByType[workspace.type]++;
      if (workspace.framework) {
        workspacesByFramework[workspace.framework]++;
      }
    }

    return {
      totalWorkspaces: workspaces.length,
      workspacesByType,
      workspacesByFramework,
      dependencyGraph,
      circularDependencies,
      orphanedWorkspaces
    };
  }

  /**
   * Find circular dependencies in the workspace graph
   */
  private findCircularDependencies(graph: Map<string, DependencyGraphNode>): string[][] {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    const cycles: string[][] = [];

    const dfs = (node: string, path: string[]): void => {
      if (recursionStack.has(node)) {
        // Found a cycle
        const cycleStart = path.indexOf(node);
        if (cycleStart !== -1) {
          cycles.push(path.slice(cycleStart).concat(node));
        }
        return;
      }

      if (visited.has(node)) {
        return;
      }

      visited.add(node);
      recursionStack.add(node);

      const graphNode = graph.get(node);
      if (graphNode) {
        for (const dep of graphNode.dependencies) {
          dfs(dep, [...path, node]);
        }
      }

      recursionStack.delete(node);
    };

    for (const [name] of graph.entries()) {
      if (!visited.has(name)) {
        dfs(name, []);
      }
    }

    return cycles;
  }

  /**
   * Get build order for workspaces
   */
  getBuildOrder(): string[] {
    const graph = this.analyzeWorkspaces().dependencyGraph;
    const visited = new Set<string>();
    const buildOrder: string[] = [];

    const visit = (name: string): void => {
      if (visited.has(name)) {
        return;
      }

      visited.add(name);
      const node = graph.get(name);
      
      if (node) {
        // Visit dependencies first
        for (const dep of node.dependencies) {
          visit(dep);
        }
      }

      buildOrder.push(name);
    };

    // Visit all workspaces
    for (const [name] of graph.entries()) {
      visit(name);
    }

    return buildOrder;
  }

  /**
   * Generate workspace dependency graph in different formats
   */
  generateDependencyGraph(format: 'text' | 'json' | 'mermaid' = 'text'): string {
    const analysis = this.analyzeWorkspaces();
    const graph = analysis.dependencyGraph;

    switch (format) {
      case 'json':
        return JSON.stringify(
          Object.fromEntries(
            Array.from(graph.entries()).map(([name, node]) => [
              name,
              {
                type: node.type,
                framework: node.framework,
                dependencies: node.dependencies,
                dependents: node.dependents,
                path: node.path
              }
            ])
          ),
          null,
          2
        );

      case 'mermaid': {
        let mermaid = 'graph TD\n';
        
        for (const [name, node] of graph.entries()) {
          // Define node shape based on type
          let nodeShape = '';
          switch (node.type) {
            case 'app':
              nodeShape = `${name}[${name}]`;
              break;
            case 'package':
              nodeShape = `${name}(${name})`;
              break;
            case 'lib':
              nodeShape = `${name}{{${name}}}`;
              break;
            case 'tool':
              nodeShape = `${name}[/${name}/]`;
              break;
          }
          
          mermaid += `  ${nodeShape}\n`;
          
          // Add dependencies
          for (const dep of node.dependencies) {
            mermaid += `  ${name} --> ${dep}\n`;
          }
        }
        
        return mermaid;
      }

      case 'text':
      default: {
        let text = 'Workspace Dependency Graph:\n';
        text += '================================\n\n';
        
        for (const [name, node] of graph.entries()) {
          text += `${name} (${node.type}${node.framework ? `, ${node.framework}` : ''})\n`;
          text += `  Path: ${node.path}\n`;
          
          if (node.dependencies.length > 0) {
            text += `  Dependencies: ${node.dependencies.join(', ')}\n`;
          }
          
          if (node.dependents.length > 0) {
            text += `  Dependents: ${node.dependents.join(', ')}\n`;
          }
          
          text += '\n';
        }
        
        if (analysis.circularDependencies.length > 0) {
          text += 'Circular Dependencies:\n';
          text += '=====================\n';
          for (const cycle of analysis.circularDependencies) {
            text += `  ${cycle.join(' -> ')}\n`;
          }
          text += '\n';
        }
        
        if (analysis.orphanedWorkspaces.length > 0) {
          text += 'Orphaned Workspaces:\n';
          text += '===================\n';
          for (const orphan of analysis.orphanedWorkspaces) {
            text += `  ${orphan}\n`;
          }
        }
        
        return text;
      }
    }
  }

  /**
   * Clear all workspaces
   */
  clear(): void {
    this.workspaces.clear();
  }

  /**
   * Get workspace statistics
   */
  getStats(): {
    total: number;
    byType: Record<WorkspaceType, number>;
    byFramework: Record<FrameworkType, number>;
  } {
    const analysis = this.analyzeWorkspaces();
    return {
      total: analysis.totalWorkspaces,
      byType: analysis.workspacesByType,
      byFramework: analysis.workspacesByFramework
    };
  }
}

// Export singleton instance
export const workspaceManager = new WorkspaceManager();

/**
 * Workspace utilities
 */
export const WorkspaceUtils = {
  /**
   * Validate workspace name
   */
  validateWorkspaceName(name: string): boolean {
    return /^[a-z0-9-]+$/.test(name) && name.length > 0 && name.length <= 50;
  },

  /**
   * Generate workspace path
   */
  generateWorkspacePath(name: string, type: WorkspaceType): string {
    const typeToFolder: Record<WorkspaceType, string> = {
      app: 'apps',
      package: 'packages',
      lib: 'libs',
      tool: 'tools'
    };
    
    return `${typeToFolder[type]}/${name}`;
  },

  /**
   * Get package manager commands
   */
  getPackageManagerCommands(pm: PackageManager): {
    install: string;
    add: string;
    remove: string;
    run: string;
    build: string;
  } {
    switch (pm) {
      case 'yarn':
        return {
          install: 'yarn install',
          add: 'yarn add',
          remove: 'yarn remove',
          run: 'yarn',
          build: 'yarn build'
        };
      case 'npm':
        return {
          install: 'npm install',
          add: 'npm install',
          remove: 'npm uninstall',
          run: 'npm run',
          build: 'npm run build'
        };
      case 'pnpm':
      default:
        return {
          install: 'pnpm install',
          add: 'pnpm add',
          remove: 'pnpm remove',
          run: 'pnpm',
          build: 'pnpm build'
        };
    }
  }
};
