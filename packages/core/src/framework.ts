import { FrameworkType, MicrofrontendConfig } from './types';

/**
 * Framework-specific configuration and utilities
 */
export interface FrameworkAdapter {
  /**
   * Framework type
   */
  type: FrameworkType;
  
  /**
   * Mount a microfrontend
   */
  mount(config: MicrofrontendConfig, container: HTMLElement): Promise<void>;
  
  /**
   * Unmount a microfrontend
   */
  unmount(config: MicrofrontendConfig, container: HTMLElement): Promise<void>;
  
  /**
   * Check if the framework is available
   */
  isAvailable(): boolean;
  
  /**
   * Get framework-specific dependencies
   */
  getDependencies(): string[];
}

/**
 * React framework adapter
 */
export class ReactAdapter implements FrameworkAdapter {
  type: FrameworkType = 'react';
  
  async mount(config: MicrofrontendConfig, container: HTMLElement): Promise<void> {
    // React mounting logic will be handled by the microfrontend itself
    // This is a placeholder for framework-specific mounting logic
    if (this.isAvailable()) {
      // Dispatch custom event to notify React microfrontend to mount
      const event = new CustomEvent('re-shell:mount', {
        detail: { config, container }
      });
      container.dispatchEvent(event);
    }
  }
  
  async unmount(config: MicrofrontendConfig, container: HTMLElement): Promise<void> {
    if (this.isAvailable()) {
      // Dispatch custom event to notify React microfrontend to unmount
      const event = new CustomEvent('re-shell:unmount', {
        detail: { config, container }
      });
      container.dispatchEvent(event);
      
      // Clear container after unmounting
      setTimeout(() => {
        container.innerHTML = '';
      }, 100);
    }
  }
  
  isAvailable(): boolean {
    return typeof window !== 'undefined' && 
           (window as any).React !== undefined;
  }
  
  getDependencies(): string[] {
    return ['react', 'react-dom'];
  }
}

/**
 * Vue framework adapter
 */
export class VueAdapter implements FrameworkAdapter {
  type: FrameworkType = 'vue';
  
  async mount(config: MicrofrontendConfig, container: HTMLElement): Promise<void> {
    if (this.isAvailable()) {
      const event = new CustomEvent('re-shell:mount', {
        detail: { config, container }
      });
      container.dispatchEvent(event);
    }
  }
  
  async unmount(config: MicrofrontendConfig, container: HTMLElement): Promise<void> {
    if (this.isAvailable()) {
      const event = new CustomEvent('re-shell:unmount', {
        detail: { config, container }
      });
      container.dispatchEvent(event);
      
      setTimeout(() => {
        container.innerHTML = '';
      }, 100);
    }
  }
  
  isAvailable(): boolean {
    return typeof window !== 'undefined' && 
           ((window as any).Vue !== undefined || (window as any).__VUE__ !== undefined);
  }
  
  getDependencies(): string[] {
    return ['vue'];
  }
}

/**
 * Svelte framework adapter
 */
export class SvelteAdapter implements FrameworkAdapter {
  type: FrameworkType = 'svelte';
  
  async mount(config: MicrofrontendConfig, container: HTMLElement): Promise<void> {
    if (this.isAvailable()) {
      const event = new CustomEvent('re-shell:mount', {
        detail: { config, container }
      });
      container.dispatchEvent(event);
    }
  }
  
  async unmount(config: MicrofrontendConfig, container: HTMLElement): Promise<void> {
    if (this.isAvailable()) {
      const event = new CustomEvent('re-shell:unmount', {
        detail: { config, container }
      });
      container.dispatchEvent(event);
      
      setTimeout(() => {
        container.innerHTML = '';
      }, 100);
    }
  }
  
  isAvailable(): boolean {
    return typeof window !== 'undefined';
  }
  
  getDependencies(): string[] {
    return [];
  }
}

/**
 * Vanilla JavaScript adapter
 */
export class VanillaAdapter implements FrameworkAdapter {
  type: FrameworkType = 'vanilla';
  
  async mount(config: MicrofrontendConfig, container: HTMLElement): Promise<void> {
    const event = new CustomEvent('re-shell:mount', {
      detail: { config, container }
    });
    container.dispatchEvent(event);
  }
  
  async unmount(config: MicrofrontendConfig, container: HTMLElement): Promise<void> {
    const event = new CustomEvent('re-shell:unmount', {
      detail: { config, container }
    });
    container.dispatchEvent(event);
    
    setTimeout(() => {
      container.innerHTML = '';
    }, 100);
  }
  
  isAvailable(): boolean {
    return true;
  }
  
  getDependencies(): string[] {
    return [];
  }
}

/**
 * Angular framework adapter
 */
export class AngularAdapter implements FrameworkAdapter {
  type: FrameworkType = 'angular';
  
  async mount(config: MicrofrontendConfig, container: HTMLElement): Promise<void> {
    if (this.isAvailable()) {
      const event = new CustomEvent('re-shell:mount', {
        detail: { config, container }
      });
      container.dispatchEvent(event);
    }
  }
  
  async unmount(config: MicrofrontendConfig, container: HTMLElement): Promise<void> {
    if (this.isAvailable()) {
      const event = new CustomEvent('re-shell:unmount', {
        detail: { config, container }
      });
      container.dispatchEvent(event);
      
      setTimeout(() => {
        container.innerHTML = '';
      }, 100);
    }
  }
  
  isAvailable(): boolean {
    return typeof window !== 'undefined' && 
           (window as any).ng !== undefined;
  }
  
  getDependencies(): string[] {
    return ['@angular/core', '@angular/platform-browser'];
  }
}

/**
 * Framework registry for managing different framework adapters
 */
export class FrameworkRegistry {
  private adapters: Map<FrameworkType, FrameworkAdapter> = new Map();
  
  constructor() {
    // Register default adapters
    this.register(new ReactAdapter());
    this.register(new VueAdapter());
    this.register(new SvelteAdapter());
    this.register(new VanillaAdapter());
    this.register(new AngularAdapter());
  }
  
  /**
   * Register a framework adapter
   */
  register(adapter: FrameworkAdapter): void {
    this.adapters.set(adapter.type, adapter);
  }
  
  /**
   * Get a framework adapter
   */
  get(type: FrameworkType): FrameworkAdapter | undefined {
    return this.adapters.get(type);
  }
  
  /**
   * Get all registered adapters
   */
  getAll(): FrameworkAdapter[] {
    return Array.from(this.adapters.values());
  }
  
  /**
   * Get available frameworks
   */
  getAvailable(): FrameworkAdapter[] {
    return this.getAll().filter(adapter => adapter.isAvailable());
  }
  
  /**
   * Detect framework from microfrontend config or DOM
   */
  detectFramework(config?: MicrofrontendConfig): FrameworkType {
    if (config?.framework) {
      return config.framework;
    }
    
    // Try to detect from available frameworks
    const available = this.getAvailable();
    if (available.length > 0) {
      return available[0].type;
    }
    
    return 'vanilla';
  }
}

// Export singleton instance
export const frameworkRegistry = new FrameworkRegistry();

/**
 * Utility functions for framework detection and management
 */
export const FrameworkUtils = {
  /**
   * Detect framework type from a URL or script
   */
  detectFromUrl(url: string): FrameworkType {
    const urlLower = url.toLowerCase();
    
    if (urlLower.includes('react')) return 'react';
    if (urlLower.includes('vue')) return 'vue';
    if (urlLower.includes('svelte')) return 'svelte';
    if (urlLower.includes('angular')) return 'angular';
    
    return 'vanilla';
  },
  
  /**
   * Get framework-specific file extensions
   */
  getFileExtensions(framework: FrameworkType): string[] {
    switch (framework) {
      case 'react':
        return ['.jsx', '.tsx'];
      case 'vue':
        return ['.vue'];
      case 'svelte':
        return ['.svelte'];
      case 'angular':
        return ['.ts', '.component.ts'];
      default:
        return ['.js', '.ts'];
    }
  },
  
  /**
   * Check if a framework supports TypeScript
   */
  supportsTypeScript(framework: FrameworkType): boolean {
    return ['react', 'vue', 'svelte', 'angular'].includes(framework);
  }
};
