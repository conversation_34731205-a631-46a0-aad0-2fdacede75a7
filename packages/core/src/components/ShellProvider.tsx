import React, { createContext, useContext, useState, ReactNode } from 'react';
import { MicrofrontendConfig } from '../types';

interface ShellContextValue {
  /**
   * List of registered microfrontends
   */
  microfrontends: MicrofrontendConfig[];
  
  /**
   * Register a new microfrontend
   */
  registerMicrofrontend: (config: MicrofrontendConfig) => void;
  
  /**
   * Unregister a microfrontend by ID
   */
  unregisterMicrofrontend: (id: string) => void;
}

// Create context with default values
// eslint-disable-next-line @typescript-eslint/no-empty-function
const ShellContext = createContext<ShellContextValue>({
  microfrontends: [],
  registerMicrofrontend: () => {/* This function is implemented in the ShellProvider */},
  unregisterMicrofrontend: () => {/* This function is implemented in the ShellProvider */},
});

export interface ShellProviderProps {
  /**
   * Initial microfrontend configurations
   */
  initialMicrofrontends?: MicrofrontendConfig[];
  
  /**
   * Child components
   */
  children: ReactNode;
}

/**
 * Provider component for the ReShell context
 */
export const ShellProvider: React.FC<ShellProviderProps> = ({
  initialMicrofrontends = [],
  children,
}) => {
  const [microfrontends, setMicrofrontends] = useState<MicrofrontendConfig[]>(initialMicrofrontends);
  
  const registerMicrofrontend = (config: MicrofrontendConfig) => {
    setMicrofrontends(prev => {
      // Check if microfrontend with the same ID already exists
      const exists = prev.some(mf => mf.id === config.id);
      
      if (exists) {
        console.warn(`Microfrontend with ID "${config.id}" is already registered. Skipping.`);
        return prev;
      }
      
      return [...prev, config];
    });
  };
  
  const unregisterMicrofrontend = (id: string) => {
    setMicrofrontends(prev => prev.filter(mf => mf.id !== id));
  };
  
  const contextValue: ShellContextValue = {
    microfrontends,
    registerMicrofrontend,
    unregisterMicrofrontend,
  };
  
  return (
    <ShellContext.Provider value={contextValue}>
      {children}
    </ShellContext.Provider>
  );
};

/**
 * Hook to access the shell context
 */
export const useShell = () => useContext(ShellContext);