import React, { useEffect, useRef } from 'react';
import { MicrofrontendConfig, LoadMicrofrontendOptions } from '../types';
import { loadMicrofrontend } from '../loader';

export interface MicrofrontendContainerProps {
  /**
   * Configuration for the microfrontend
   */
  config: MicrofrontendConfig;
  
  /**
   * Options for loading the microfrontend
   */
  options?: LoadMicrofrontendOptions;
  
  /**
   * Additional CSS class names
   */
  className?: string;
}

/**
 * Container component for loading and rendering a microfrontend
 */
export const MicrofrontendContainer: React.FC<MicrofrontendContainerProps> = ({
  config,
  options,
  className,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { id, containerId } = config;
  
  useEffect(() => {
    if (containerRef.current) {
      // Set the ID on the container element
      containerRef.current.id = containerId;
      
      // Load the microfrontend
      loadMicrofrontend(config, options).catch(error => {
        console.error(`Failed to load microfrontend "${id}":`, error);
      });
    }
    
    // Cleanup function
    return () => {
      // Implement cleanup logic if needed
    };
  }, [id, containerId, config, options]);
  
  return (
    <div
      ref={containerRef}
      className={`reshell-microfrontend-container ${className || ''}`}
      data-microfrontend-id={id}
    />
  );
};