/**
 * Supported framework types
 */
export type FrameworkType = 'react' | 'vue' | 'svelte' | 'vanilla' | 'angular';

/**
 * Microfrontend lifecycle states
 */
export type MicrofrontendLifecycleState = 'loading' | 'loaded' | 'mounted' | 'unmounted' | 'error';

/**
 * Environment types
 */
export type Environment = 'development' | 'production' | 'test';

/**
 * Package manager types
 */
export type PackageManager = 'npm' | 'yarn' | 'pnpm';

/**
 * Workspace types
 */
export type WorkspaceType = 'app' | 'package' | 'lib' | 'tool';

/**
 * Configuration for a microfrontend
 */
export interface MicrofrontendConfig {
  /**
   * Unique identifier for the microfrontend
   */
  id: string;

  /**
   * Name of the microfrontend
   */
  name: string;

  /**
   * URL to load the microfrontend from
   */
  url: string;

  /**
   * DOM element ID where the microfrontend should be mounted
   */
  containerId: string;

  /**
   * Optional route path for the microfrontend
   */
  route?: string;

  /**
   * Team that owns this microfrontend
   */
  team?: string;

  /**
   * Framework used by the microfrontend
   */
  framework?: FrameworkType;

  /**
   * Version of the microfrontend
   */
  version?: string;

  /**
   * Description of the microfrontend
   */
  description?: string;

  /**
   * Organization that owns this microfrontend
   */
  organization?: string;

  /**
   * Development server port
   */
  port?: number;

  /**
   * Environment-specific configurations
   */
  environments?: Record<Environment, Partial<MicrofrontendConfig>>;

  /**
   * Custom properties for framework-specific configurations
   */
  props?: Record<string, unknown>;

  /**
   * Dependencies required by this microfrontend
   */
  dependencies?: string[];

  /**
   * CSS files to load with this microfrontend
   */
  stylesheets?: string[];

  /**
   * Whether this microfrontend supports hot module replacement
   */
  hmr?: boolean;
}

/**
 * Options for loading a microfrontend
 */
export interface LoadMicrofrontendOptions {
  /**
   * Whether to load the microfrontend asynchronously
   * @default true
   */
  async?: boolean;

  /**
   * Callback function to run when the microfrontend is loaded
   */
  onLoad?: () => void;

  /**
   * Callback function to run if loading the microfrontend fails
   */
  onError?: (error: Error) => void;

  /**
   * Timeout in milliseconds for loading the microfrontend
   * @default 30000
   */
  timeout?: number;

  /**
   * Whether to enable performance monitoring
   * @default false
   */
  enablePerformanceMonitoring?: boolean;

  /**
   * Custom loading strategy
   */
  loadingStrategy?: 'eager' | 'lazy' | 'preload';

  /**
   * Environment to load for
   */
  environment?: Environment;
}

/**
 * Microfrontend lifecycle hooks
 */
export interface MicrofrontendLifecycleHooks {
  /**
   * Called before the microfrontend is mounted
   */
  beforeMount?: (config: MicrofrontendConfig) => void | Promise<void>;

  /**
   * Called after the microfrontend is mounted
   */
  afterMount?: (config: MicrofrontendConfig) => void | Promise<void>;

  /**
   * Called before the microfrontend is unmounted
   */
  beforeUnmount?: (config: MicrofrontendConfig) => void | Promise<void>;

  /**
   * Called after the microfrontend is unmounted
   */
  afterUnmount?: (config: MicrofrontendConfig) => void | Promise<void>;

  /**
   * Called when an error occurs
   */
  onError?: (error: Error, config: MicrofrontendConfig) => void;
}

/**
 * Performance metrics for a microfrontend
 */
export interface MicrofrontendPerformanceMetrics {
  /**
   * Time taken to load the microfrontend script
   */
  loadTime: number;

  /**
   * Time taken to mount the microfrontend
   */
  mountTime: number;

  /**
   * Bundle size in bytes
   */
  bundleSize?: number;

  /**
   * Memory usage in bytes
   */
  memoryUsage?: number;

  /**
   * Timestamp when metrics were collected
   */
  timestamp: number;
}

/**
 * Route configuration for microfrontends
 */
export interface RouteConfig {
  /**
   * Route path pattern
   */
  path: string;

  /**
   * Microfrontend ID to load for this route
   */
  microfrontendId: string;

  /**
   * Whether this route should be exact match
   */
  exact?: boolean;

  /**
   * Route guards
   */
  guards?: RouteGuard[];

  /**
   * Route metadata
   */
  meta?: Record<string, unknown>;
}

/**
 * Route guard function
 */
export type RouteGuard = (to: RouteConfig, from?: RouteConfig) => boolean | Promise<boolean>;

/**
 * Workspace configuration
 */
export interface WorkspaceConfig {
  /**
   * Workspace name
   */
  name: string;

  /**
   * Workspace type
   */
  type: WorkspaceType;

  /**
   * Framework used
   */
  framework?: FrameworkType;

  /**
   * Version
   */
  version: string;

  /**
   * Dependencies
   */
  dependencies?: Record<string, string>;

  /**
   * Dev dependencies
   */
  devDependencies?: Record<string, string>;

  /**
   * Build scripts
   */
  scripts?: Record<string, string>;

  /**
   * Workspace path
   */
  path: string;
}

/**
 * Enhanced event data with type safety
 */
export interface EventData<T = Record<string, unknown>> {
  /**
   * Event payload
   */
  payload: T;

  /**
   * Event timestamp
   */
  timestamp: number;

  /**
   * Event source (microfrontend ID)
   */
  source?: string;

  /**
   * Event namespace
   */
  namespace?: string;

  /**
   * Event metadata
   */
  meta?: Record<string, unknown>;
}

/**
 * Event handler with enhanced type safety
 */
export type EventHandler<T = Record<string, unknown>> = (data: EventData<T>) => void;

/**
 * Event subscription
 */
export interface EventSubscription {
  /**
   * Event name
   */
  event: string;

  /**
   * Handler function
   */
  handler: EventHandler;

  /**
   * Subscription ID
   */
  id: string;

  /**
   * Subscription timestamp
   */
  timestamp: number;

  /**
   * Optional namespace
   */
  namespace?: string;
}

/**
 * Development tools configuration
 */
export interface DevToolsConfig {
  /**
   * Whether to enable development tools
   */
  enabled: boolean;

  /**
   * Whether to enable event logging
   */
  logEvents?: boolean;

  /**
   * Whether to enable performance monitoring
   */
  monitorPerformance?: boolean;

  /**
   * Whether to enable hot module replacement
   */
  enableHMR?: boolean;

  /**
   * Custom debug functions
   */
  debugFunctions?: Record<string, (...args: unknown[]) => void>;
}

/**
 * Error boundary configuration
 */
export interface ErrorBoundaryConfig {
  /**
   * Whether to enable error boundaries
   */
  enabled: boolean;

  /**
   * Custom error handler
   */
  onError?: (error: Error, errorInfo: { componentStack: string }) => void;

  /**
   * Fallback component to render on error
   */
  fallbackComponent?: React.ComponentType<{ error: Error; retry: () => void }>;

  /**
   * Whether to automatically retry on error
   */
  autoRetry?: boolean;

  /**
   * Maximum number of retry attempts
   */
  maxRetries?: number;
}

/**
 * Shell configuration
 */
export interface ShellConfig {
  /**
   * Shell name
   */
  name: string;

  /**
   * Shell version
   */
  version: string;

  /**
   * Environment
   */
  environment: Environment;

  /**
   * Base URL for microfrontends
   */
  baseUrl?: string;

  /**
   * Default loading options
   */
  defaultLoadingOptions?: LoadMicrofrontendOptions;

  /**
   * Global lifecycle hooks
   */
  lifecycleHooks?: MicrofrontendLifecycleHooks;

  /**
   * Development tools configuration
   */
  devTools?: DevToolsConfig;

  /**
   * Error boundary configuration
   */
  errorBoundary?: ErrorBoundaryConfig;

  /**
   * Route configurations
   */
  routes?: RouteConfig[];
}
