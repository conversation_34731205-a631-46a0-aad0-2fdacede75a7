import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { performanceMonitor, PerformanceUtils } from '../src/performance';

// Mock performance API
const mockPerformance = {
  now: vi.fn(() => Date.now()),
  mark: vi.fn(),
  measure: vi.fn(),
  getEntriesByName: vi.fn(() => [{ duration: 100 }]),
  memory: {
    usedJSHeapSize: 1024 * 1024 // 1MB
  }
};

Object.defineProperty(global, 'performance', {
  value: mockPerformance,
  writable: true
});

describe('Performance Monitor', () => {
  beforeEach(() => {
    performanceMonitor.clearMetrics();
    performanceMonitor.updateConfig({ enabled: true });
    vi.clearAllMocks();
  });

  afterEach(() => {
    performanceMonitor.clearMetrics();
  });

  describe('Basic functionality', () => {
    it('should track load and mount times', () => {
      // Setup
      let currentTime = 1000;
      mockPerformance.now.mockImplementation(() => currentTime);

      // Act
      performanceMonitor.startLoad('test-mf');
      currentTime += 500; // 500ms load time
      performanceMonitor.endLoad('test-mf', 1024);
      
      performanceMonitor.startMount('test-mf');
      currentTime += 200; // 200ms mount time
      performanceMonitor.endMount('test-mf');

      // Assert
      const metrics = performanceMonitor.getMetrics('test-mf');
      expect(metrics).toHaveLength(1);
      expect(metrics[0].loadTime).toBe(500);
      expect(metrics[0].mountTime).toBe(200);
      expect(metrics[0].bundleSize).toBe(1024);
    });

    it('should handle disabled monitoring', () => {
      // Setup
      performanceMonitor.updateConfig({ enabled: false });

      // Act
      performanceMonitor.startLoad('test-mf');
      performanceMonitor.endLoad('test-mf');
      performanceMonitor.startMount('test-mf');
      performanceMonitor.endMount('test-mf');

      // Assert
      const metrics = performanceMonitor.getMetrics('test-mf');
      expect(metrics).toHaveLength(0);
    });

    it('should collect memory usage when available', () => {
      // Setup
      let currentTime = 1000;
      let memoryUsage = 1024 * 1024; // Start with 1MB
      
      mockPerformance.now.mockImplementation(() => currentTime);
      Object.defineProperty(mockPerformance, 'memory', {
        get: () => ({ usedJSHeapSize: memoryUsage })
      });

      // Act
      performanceMonitor.startLoad('test-mf');
      currentTime += 500;
      memoryUsage += 512 * 1024; // Add 512KB
      performanceMonitor.endLoad('test-mf');
      
      performanceMonitor.startMount('test-mf');
      currentTime += 200;
      memoryUsage += 256 * 1024; // Add 256KB
      performanceMonitor.endMount('test-mf');

      // Assert
      const metrics = performanceMonitor.getMetrics('test-mf');
      expect(metrics[0].memoryUsage).toBe(768 * 1024); // 768KB increase
    });
  });

  describe('Configuration and thresholds', () => {
    it('should log to console when enabled', () => {
      // Setup
      const consoleSpy = vi.spyOn(console, 'group').mockImplementation(() => {});
      const consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      const consoleGroupEndSpy = vi.spyOn(console, 'groupEnd').mockImplementation(() => {});
      
      performanceMonitor.updateConfig({ 
        enabled: true, 
        logToConsole: true 
      });

      let currentTime = 1000;
      mockPerformance.now.mockImplementation(() => currentTime);

      // Act
      performanceMonitor.startLoad('test-mf');
      currentTime += 500;
      performanceMonitor.endLoad('test-mf');
      performanceMonitor.startMount('test-mf');
      currentTime += 200;
      performanceMonitor.endMount('test-mf');

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith('[Performance] test-mf');
      expect(consoleLogSpy).toHaveBeenCalledWith('Load time: 500.00ms');
      expect(consoleLogSpy).toHaveBeenCalledWith('Mount time: 200.00ms');
      expect(consoleGroupEndSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
      consoleLogSpy.mockRestore();
      consoleGroupEndSpy.mockRestore();
    });

    it('should warn when thresholds are exceeded', () => {
      // Setup
      const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      performanceMonitor.updateConfig({
        enabled: true,
        thresholds: {
          loadTime: 300,
          mountTime: 100
        }
      });

      let currentTime = 1000;
      mockPerformance.now.mockImplementation(() => currentTime);

      // Act
      performanceMonitor.startLoad('test-mf');
      currentTime += 500; // Exceeds 300ms threshold
      performanceMonitor.endLoad('test-mf');
      performanceMonitor.startMount('test-mf');
      currentTime += 200; // Exceeds 100ms threshold
      performanceMonitor.endMount('test-mf');

      // Assert
      expect(consoleWarnSpy).toHaveBeenCalledWith('[Performance Warning] test-mf:');
      expect(consoleWarnSpy).toHaveBeenCalledWith('  - Load time (500.00ms) exceeds threshold (300ms)');
      expect(consoleWarnSpy).toHaveBeenCalledWith('  - Mount time (200.00ms) exceeds threshold (100ms)');

      consoleWarnSpy.mockRestore();
    });

    it('should call custom collector when provided', () => {
      // Setup
      const customCollector = vi.fn();
      performanceMonitor.updateConfig({
        enabled: true,
        customCollector
      });

      let currentTime = 1000;
      mockPerformance.now.mockImplementation(() => currentTime);

      // Act
      performanceMonitor.startLoad('test-mf');
      currentTime += 500;
      performanceMonitor.endLoad('test-mf');
      performanceMonitor.startMount('test-mf');
      currentTime += 200;
      performanceMonitor.endMount('test-mf');

      // Assert
      expect(customCollector).toHaveBeenCalledWith(
        expect.objectContaining({
          loadTime: 500,
          mountTime: 200,
          timestamp: expect.any(Number)
        })
      );
    });
  });

  describe('Metrics aggregation', () => {
    it('should calculate average metrics', () => {
      // Setup
      let currentTime = 1000;
      mockPerformance.now.mockImplementation(() => currentTime);

      // Record multiple metrics
      for (let i = 0; i < 3; i++) {
        performanceMonitor.startLoad('test-mf');
        currentTime += 400 + i * 100; // 400ms, 500ms, 600ms
        performanceMonitor.endLoad('test-mf');
        performanceMonitor.startMount('test-mf');
        currentTime += 100 + i * 50; // 100ms, 150ms, 200ms
        performanceMonitor.endMount('test-mf');
      }

      // Act
      const avgMetrics = performanceMonitor.getAverageMetrics('test-mf');

      // Assert
      expect(avgMetrics).not.toBeNull();
      expect(avgMetrics!.loadTime).toBe(500); // Average of 400, 500, 600
      expect(avgMetrics!.mountTime).toBe(150); // Average of 100, 150, 200
    });

    it('should return null for non-existent microfrontend', () => {
      const avgMetrics = performanceMonitor.getAverageMetrics('non-existent');
      expect(avgMetrics).toBeNull();
    });

    it('should provide performance summary', () => {
      // Setup
      let currentTime = 1000;
      mockPerformance.now.mockImplementation(() => currentTime);

      // Record metrics for multiple microfrontends
      ['mf1', 'mf2'].forEach(id => {
        performanceMonitor.startLoad(id);
        currentTime += 500;
        performanceMonitor.endLoad(id, 1024);
        performanceMonitor.startMount(id);
        currentTime += 200;
        performanceMonitor.endMount(id);
      });

      // Act
      const summary = performanceMonitor.getSummary();

      // Assert
      expect(summary.totalMicrofrontends).toBe(2);
      expect(summary.averageLoadTime).toBe(500);
      expect(summary.averageMountTime).toBe(200);
      expect(summary.totalBundleSize).toBe(2048);
      expect(summary.slowestMicrofrontend).toEqual({
        id: expect.any(String),
        loadTime: 500
      });
    });
  });

  describe('Cleanup', () => {
    it('should clear metrics for specific microfrontend', () => {
      // Setup
      let currentTime = 1000;
      mockPerformance.now.mockImplementation(() => currentTime);

      performanceMonitor.startLoad('test-mf');
      currentTime += 500;
      performanceMonitor.endLoad('test-mf');
      performanceMonitor.startMount('test-mf');
      currentTime += 200;
      performanceMonitor.endMount('test-mf');

      expect(performanceMonitor.getMetrics('test-mf')).toHaveLength(1);

      // Act
      performanceMonitor.clearMetrics('test-mf');

      // Assert
      expect(performanceMonitor.getMetrics('test-mf')).toHaveLength(0);
    });

    it('should clear all metrics', () => {
      // Setup
      let currentTime = 1000;
      mockPerformance.now.mockImplementation(() => currentTime);

      ['mf1', 'mf2'].forEach(id => {
        performanceMonitor.startLoad(id);
        currentTime += 500;
        performanceMonitor.endLoad(id);
        performanceMonitor.startMount(id);
        currentTime += 200;
        performanceMonitor.endMount(id);
      });

      expect(performanceMonitor.getAllMetrics().size).toBe(2);

      // Act
      performanceMonitor.clearMetrics();

      // Assert
      expect(performanceMonitor.getAllMetrics().size).toBe(0);
    });
  });
});

describe('Performance Utils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Measurement utilities', () => {
    it('should measure async function execution time', async () => {
      // Setup
      let currentTime = 1000;
      mockPerformance.now.mockImplementation(() => currentTime);

      const asyncFn = async () => {
        currentTime += 500; // Simulate 500ms execution
        return 'result';
      };

      // Act
      const { result, duration } = await PerformanceUtils.measureAsync(asyncFn);

      // Assert
      expect(result).toBe('result');
      expect(duration).toBe(500);
    });

    it('should measure sync function execution time', () => {
      // Setup
      let currentTime = 1000;
      mockPerformance.now.mockImplementation(() => currentTime);

      const syncFn = () => {
        currentTime += 300; // Simulate 300ms execution
        return 'sync-result';
      };

      // Act
      const { result, duration } = PerformanceUtils.measure(syncFn);

      // Assert
      expect(result).toBe('sync-result');
      expect(duration).toBe(300);
    });

    it('should create performance marks', () => {
      // Act
      PerformanceUtils.mark('test-mark');

      // Assert
      expect(mockPerformance.mark).toHaveBeenCalledWith('test-mark');
    });

    it('should measure between marks', () => {
      // Setup
      mockPerformance.getEntriesByName.mockReturnValue([{ duration: 250 }]);

      // Act
      const duration = PerformanceUtils.measureBetweenMarks('test-measure', 'start', 'end');

      // Assert
      expect(mockPerformance.measure).toHaveBeenCalledWith('test-measure', 'start', 'end');
      expect(mockPerformance.getEntriesByName).toHaveBeenCalledWith('test-measure');
      expect(duration).toBe(250);
    });

    it('should handle measurement errors gracefully', () => {
      // Setup
      mockPerformance.measure.mockImplementation(() => {
        throw new Error('Measurement failed');
      });
      const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      // Act
      const duration = PerformanceUtils.measureBetweenMarks('test-measure', 'start', 'end');

      // Assert
      expect(duration).toBeNull();
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        'Failed to measure between marks:',
        expect.any(Error)
      );

      consoleWarnSpy.mockRestore();
    });
  });
});
