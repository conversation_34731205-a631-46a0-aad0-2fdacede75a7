import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { eventBus } from '../src/eventBus';

describe('Enhanced Event Bus', () => {
  beforeEach(() => {
    eventBus.clear();
    eventBus.clearHistory();
    eventBus.setDebugMode(false);
  });

  afterEach(() => {
    eventBus.clear();
    eventBus.clearHistory();
  });

  describe('Basic functionality', () => {
    it('should register event listeners and emit events with enhanced data structure', () => {
      // Setup
      const mockCallback = vi.fn();
      const subscriptionId = eventBus.on('test-event', mockCallback);

      // Act
      eventBus.emit('test-event', { data: 'test-data' });

      // Assert
      expect(mockCallback).toHaveBeenCalledOnce();
      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          payload: { data: 'test-data' },
          timestamp: expect.any(Number),
          source: undefined,
          namespace: undefined,
          meta: undefined,
        })
      );
      expect(subscriptionId).toMatch(/^sub_\d+_[a-z0-9]+$/);
    });

    it('should support subscription IDs for unsubscribing', () => {
      // Setup
      const mockCallback = vi.fn();
      const subscriptionId = eventBus.on('test-subscription', mockCallback);

      // Act
      eventBus.emit('test-subscription', { data: 'before-removal' });
      eventBus.off(subscriptionId);
      eventBus.emit('test-subscription', { data: 'after-removal' });

      // Assert
      expect(mockCallback).toHaveBeenCalledOnce();
      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          payload: { data: 'before-removal' },
        })
      );
    });

    it('should support once subscription', () => {
      // Setup
      const mockCallback = vi.fn();
      eventBus.once('test-once', mockCallback);

      // Act
      eventBus.emit('test-once', { data: 'first' });
      eventBus.emit('test-once', { data: 'second' });

      // Assert
      expect(mockCallback).toHaveBeenCalledOnce();
      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          payload: { data: 'first' },
        })
      );
    });
  });

  describe('Enhanced features', () => {
    it('should emit events with source and namespace', () => {
      // Setup
      const mockCallback = vi.fn();
      eventBus.on('test-enhanced', mockCallback);

      // Act
      eventBus.emit(
        'test-enhanced',
        { data: 'test' },
        {
          source: 'test-source',
          namespace: 'test-namespace',
          meta: { custom: 'metadata' },
        }
      );

      // Assert
      expect(mockCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          payload: { data: 'test' },
          source: 'test-source',
          namespace: 'test-namespace',
          meta: { custom: 'metadata' },
        })
      );
    });

    it('should maintain event history', () => {
      // Act
      eventBus.emit('event1', { data: 'first' });
      eventBus.emit('event2', { data: 'second' });

      // Assert
      const history = eventBus.getEventHistory();
      expect(history).toHaveLength(2);
      expect(history[0].payload).toEqual({ data: 'first' });
      expect(history[1].payload).toEqual({ data: 'second' });
    });

    it('should clear event history', () => {
      // Setup
      eventBus.emit('event1', { data: 'test' });
      expect(eventBus.getEventHistory()).toHaveLength(1);

      // Act
      eventBus.clearHistory();

      // Assert
      expect(eventBus.getEventHistory()).toHaveLength(0);
    });

    it('should provide subscription management', () => {
      // Setup
      const mockCallback1 = vi.fn();
      const mockCallback2 = vi.fn();
      const sub1 = eventBus.on('event1', mockCallback1);
      const sub2 = eventBus.on('event2', mockCallback2);

      // Act
      const subscriptions = eventBus.getSubscriptions();

      // Assert
      expect(subscriptions).toHaveLength(2);
      expect(subscriptions.map(s => s.id)).toContain(sub1);
      expect(subscriptions.map(s => s.id)).toContain(sub2);
    });

    it('should provide event statistics', () => {
      // Setup
      eventBus.on('event1', vi.fn());
      eventBus.on('event2', vi.fn());
      eventBus.emit('event1', { data: 'test' });

      // Act
      const stats = eventBus.getStats();

      // Assert
      expect(stats.totalEvents).toBe(2);
      expect(stats.totalSubscriptions).toBe(2);
      expect(stats.eventTypes).toContain('event1');
      expect(stats.eventTypes).toContain('event2');
      expect(stats.historySize).toBe(1);
    });
  });

  describe('Debug mode', () => {
    it('should enable and disable debug mode', () => {
      // Setup
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      // Act
      eventBus.setDebugMode(true);
      eventBus.on('debug-test', vi.fn());

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith('[EventBus] Debug mode enabled');
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('[EventBus] Subscribed to "debug-test"')
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Error handling', () => {
    it('should handle errors in event handlers gracefully', () => {
      // Setup
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const errorCallback = vi.fn(() => {
        throw new Error('Handler error');
      });
      const normalCallback = vi.fn();

      eventBus.on('error-test', errorCallback);
      eventBus.on('error-test', normalCallback);

      // Act
      eventBus.emit('error-test', { data: 'test' });

      // Assert
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error in event handler for "error-test":',
        expect.any(Error)
      );
      expect(normalCallback).toHaveBeenCalled(); // Other handlers should still execute

      consoleErrorSpy.mockRestore();
    });

    it('should not error when emitting event with no listeners', () => {
      expect(() => {
        eventBus.emit('no-listeners', { data: 'test' });
      }).not.toThrow();
    });

    it('should not error when removing nonexistent subscription', () => {
      expect(() => {
        eventBus.off('nonexistent-subscription-id');
      }).not.toThrow();
    });
  });
});
