# Changelog

All notable changes to the `@re-shell/core` package will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.3.2] - 2024-01-25

### Fixed
- Documentation consistency and accuracy improvements
- TypeScript type definition exports
- Build artifact optimization
- Performance monitoring reliability improvements

### Changed
- Enhanced documentation with comprehensive examples and migration guides
- Improved error messages and debugging information
- Better integration with package bundlers and build tools

## [0.3.1] - 2024-01-20

### Fixed
- Build configuration issues affecting production bundles
- Memory leak in event subscription cleanup
- Route guard execution order

### Changed
- Improved performance monitoring accuracy
- Enhanced TypeScript IntelliSense support

## [0.3.0] - 2024-01-15

### Added

- **Multi-framework support**: Added support for React, Vue, Svelte, Angular, and Vanilla JS
- **Enhanced event bus**: Type-safe events with namespacing, history, and subscription management
- **Routing system**: Complete routing solution with guards, parameters, and navigation events
- **Performance monitoring**: Built-in performance metrics collection and analysis with configurable thresholds
- **Development tools**: Debug panel, HMR support, and comprehensive debugging utilities
- **Workspace management**: Monorepo support with dependency analysis and build orchestration
- **Loading strategies**: Eager, lazy, and preload options for microfrontend loading
- **Lifecycle hooks**: Complete mount/unmount lifecycle with before/after hooks
- **Framework adapters**: Pluggable framework adapters for different microfrontend types
- **Error boundaries**: Enhanced error handling and recovery mechanisms
- **Memory management**: Automatic cleanup and memory leak prevention
- **Hot module replacement**: Live reloading support for development
- **Bundle optimization**: Code splitting and asset optimization support

### Changed

- **BREAKING**: Event bus API now uses enhanced data structure with payload, timestamp, and metadata
- **BREAKING**: `loadMicrofrontend` function signature now includes optional lifecycle hooks parameter
- **BREAKING**: Performance monitoring must be explicitly enabled via options
- Improved error handling with better error messages and recovery options
- Enhanced TypeScript support with stricter type definitions
- Refactored loader to support multiple loading strategies and framework adapters
- Updated React components to support new features and better error handling

### Enhanced

- Event bus now supports subscription IDs for easier unsubscription
- Performance monitoring includes bundle size and memory usage tracking
- Development tools provide visual debugging interface
- Routing system supports dynamic parameters and query strings
- Workspace management includes circular dependency detection
- Framework detection and automatic adapter selection

### Fixed

- Memory leaks in event listeners and microfrontend instances
- Race conditions in concurrent microfrontend loading
- Error propagation in lifecycle hooks
- Performance monitoring accuracy
- TypeScript type inference issues

## [0.2.0] - 2023-09-20

### Added

- Enhanced microfrontend lifecycle management
- Improved event bus with better type safety
- Added support for nested routes in the routing system
- Integrated with the new test application
- Added performance monitoring utilities

### Changed

- Refactored the ShellProvider component for better extensibility
- Improved error handling and boundary components
- Updated React 18 integration with better concurrent mode support

### Fixed

- Fixed memory leaks in event listeners
- Resolved routing conflicts with nested microfrontends
- Fixed issue with state synchronization between microfrontends

## [0.1.0] - 2023-08-15

### Added

- Initial release of core framework
- Basic microfrontend loading functionality
- Event bus for communication
- Simple routing system
- React integration components
