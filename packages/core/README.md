# @re-shell/core

The core framework for Re-Shell microfrontend applications. This package provides the essential functionality for loading, managing, and orchestrating microfrontends in a shell application.

## 🌟 Overview

`@re-shell/core` is the foundational package of the Re-Shell microfrontend ecosystem. It provides a robust, type-safe, and performant solution for building and managing complex microfrontend architectures. Whether you're building a small application or an enterprise-scale system, Re-Shell Core offers the tools and abstractions you need to succeed.

### Key Benefits

- **🔄 Seamless Integration**: Works with React, Vue, Svelte, Angular, and Vanilla JS
- **📊 Built-in Monitoring**: Real-time performance metrics and debugging tools  
- **🛡️ Type Safety**: Full TypeScript support with comprehensive type definitions
- **🚀 Developer Experience**: Rich development tools and debugging capabilities
- **📈 Scalable Architecture**: Designed for enterprise-scale applications
- **🔧 Framework Agnostic**: Use with any modern frontend framework

## 🏗️ Architecture

The Re-Shell Core architecture is built around several key modules that work together to provide a comprehensive microfrontend solution:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Loader       │    │   Event Bus     │    │   Router        │
│  - loadMF()     │    │  - emit()       │    │  - navigate()   │
│  - unmountMF()  │◄──►│  - on()         │◄──►│  - addRoute()   │
│  - lifecycle    │    │  - namespaces   │    │  - guards       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                       ▲                       ▲
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Performance    │    │   DevTools      │    │   Workspace     │
│  - metrics      │    │  - debugPanel   │    │  - analysis     │
│  - monitoring   │    │  - logging      │    │  - buildOrder   │
│  - thresholds   │    │  - HMR          │    │  - dependencies │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**Current Version**: 0.3.2 - Enhanced with improved TypeScript definitions, optimized loading strategies, and extended framework support.

### Core Modules

1. **Loader Module** (`loader.ts`): Handles microfrontend loading, mounting, and lifecycle management
2. **Event Bus** (`eventBus.ts`): Provides type-safe inter-microfrontend communication
3. **Router** (`routing.ts`): Manages route-based microfrontend loading and navigation
4. **Performance Monitor** (`performance.ts`): Tracks and analyzes microfrontend performance
5. **DevTools** (`dev-tools.ts`): Development-time debugging and monitoring tools
6. **Workspace Manager** (`workspace.ts`): Manages monorepo workspaces and dependencies
7. **Framework Support** (`framework.ts`): Provides framework-specific integration utilities

## Features

### 🚀 Enhanced Microfrontend Loading
- **Multi-framework support**: React, Vue, Svelte, Angular, and Vanilla JS
- **Lifecycle management**: Complete mount/unmount lifecycle with hooks
- **Loading strategies**: Eager, lazy, and preload options
- **Performance monitoring**: Built-in metrics collection and analysis
- **Error boundaries**: Robust error handling and recovery

### 🔄 Advanced Event System
- **Type-safe events**: Enhanced event bus with TypeScript support
- **Event namespacing**: Organize events by namespace
- **Event history**: Debug and replay events
- **Subscription management**: Easy subscribe/unsubscribe with IDs

### 🛣️ Routing System
- **Route-based loading**: Load microfrontends based on routes
- **Route guards**: Authentication and permission checks
- **Dynamic parameters**: Support for route parameters and query strings
- **Navigation events**: Listen to route changes

### 🏗️ Workspace Management
- **Monorepo support**: Manage multiple workspaces
- **Dependency analysis**: Analyze workspace dependencies
- **Build orchestration**: Coordinate builds across workspaces
- **Package manager integration**: Support for npm, yarn, and pnpm

### 🔧 Development Tools
- **Debug panel**: Visual debugging interface
- **Hot module replacement**: Live reloading support
- **Performance insights**: Real-time performance monitoring
- **Event logging**: Comprehensive event debugging

## Installation

```bash
npm install @re-shell/core
# or
yarn add @re-shell/core
# or
pnpm add @re-shell/core
```

## Quick Start

### Basic Usage

```typescript
import { loadMicrofrontend, eventBus } from '@re-shell/core';

// Load a microfrontend
await loadMicrofrontend({
  id: 'my-microfrontend',
  name: 'My Microfrontend',
  url: '/microfrontends/my-mf.js',
  containerId: 'mf-container',
  framework: 'react'
});

// Listen for events
eventBus.on('user:selected', (data) => {
  console.log('User selected:', data.payload);
});

// Emit events
eventBus.emit('app:ready', { timestamp: Date.now() });
```

### React Integration

```tsx
import React from 'react';
import { ShellProvider, MicrofrontendContainer } from '@re-shell/core';

const App = () => {
  const microfrontendConfig = {
    id: 'dashboard',
    name: 'Dashboard',
    url: '/microfrontends/dashboard.js',
    containerId: 'dashboard-container',
    framework: 'react'
  };

  return (
    <ShellProvider>
      <div className="app">
        <header>My Shell App</header>
        <main>
          <MicrofrontendContainer 
            config={microfrontendConfig}
            options={{ enablePerformanceMonitoring: true }}
          />
        </main>
      </div>
    </ShellProvider>
  );
};
```

### Advanced Configuration

```typescript
import { 
  loadMicrofrontend, 
  performanceMonitor, 
  devTools,
  router 
} from '@re-shell/core';

// Configure performance monitoring
performanceMonitor.updateConfig({
  enabled: true,
  logToConsole: true,
  thresholds: {
    loadTime: 2000, // 2 seconds
    mountTime: 500,  // 500ms
    bundleSize: 1024 * 1024 // 1MB
  }
});

// Configure development tools
devTools.updateConfig({
  enabled: process.env.NODE_ENV === 'development',
  logEvents: true,
  monitorPerformance: true,
  enableHMR: true
});

// Set up routing
router.addRoutes([
  {
    path: '/dashboard',
    microfrontendId: 'dashboard',
    exact: true
  },
  {
    path: '/users/:id',
    microfrontendId: 'user-detail',
    guards: [authGuard]
  }
]);

// Load with lifecycle hooks
await loadMicrofrontend(
  {
    id: 'dashboard',
    name: 'Dashboard',
    url: '/microfrontends/dashboard.js',
    containerId: 'dashboard-container',
    framework: 'react'
  },
  {
    enablePerformanceMonitoring: true,
    loadingStrategy: 'lazy'
  },
  {
    beforeMount: async (config) => {
      console.log('About to mount:', config.name);
    },
    afterMount: async (config) => {
      console.log('Mounted:', config.name);
    },
    onError: (error, config) => {
      console.error('Error loading:', config.name, error);
    }
  }
);
```

## API Reference

### Core Functions

#### `loadMicrofrontend(config, options?, hooks?)`
Load a single microfrontend with enhanced lifecycle management.

#### `loadMicrofrontends(configs, options?, hooks?)`
Load multiple microfrontends concurrently.

#### `unmountMicrofrontend(id, hooks?)`
Unmount a specific microfrontend.

#### `reloadMicrofrontend(id, options?, hooks?)`
Reload a microfrontend (unmount then load).

### Event Bus

#### `eventBus.on<T>(event, handler)`
Subscribe to events with type safety.

#### `eventBus.emit<T>(event, payload, options?)`
Emit events with enhanced data structure.

#### `eventBus.once<T>(event, handler)`
Subscribe to an event once.

#### `eventBus.off(subscriptionId)`
Unsubscribe using subscription ID.

### Performance Monitoring

#### `performanceMonitor.updateConfig(config)`
Update performance monitoring configuration.

#### `performanceMonitor.getMetrics(microfrontendId)`
Get performance metrics for a specific microfrontend.

#### `performanceMonitor.getSummary()`
Get overall performance summary.

### Routing

#### `router.addRoute(route)`
Add a single route configuration.

#### `router.navigate(path, options?)`
Navigate to a specific path.

#### `router.getCurrentRoute()`
Get the current active route.

### Workspace Management

#### `workspaceManager.registerWorkspace(config)`
Register a workspace configuration.

#### `workspaceManager.analyzeWorkspaces()`
Analyze workspace dependencies and relationships.

#### `workspaceManager.getBuildOrder()`
Get optimal build order for workspaces.

## Framework Support

### React
- Full TypeScript support
- Hook-based integration
- Error boundaries
- Hot module replacement

### Vue 3
- Composition API support
- TypeScript integration
- Single File Components
- Reactive state management

### Svelte
- SvelteKit compatibility
- TypeScript preprocessing
- Reactive statements
- Scoped styling

### Angular
- Standalone components
- Dependency injection
- TypeScript support
- Zone.js integration

### Vanilla JavaScript
- No framework dependencies
- Custom element support
- ES modules
- Legacy compatibility

## Development Tools

Access development tools via `window.__RE_SHELL_DEV__` in development mode:

```javascript
// Show debug panel
window.__RE_SHELL_DEV__.showDebugPanel();

// Get performance metrics
window.__RE_SHELL_DEV__.getPerformanceMetrics();

// Enable event logging
window.__RE_SHELL_DEV__.enableEventLogging();

// Get event history
window.__RE_SHELL_DEV__.getEventHistory();
```

## Performance Optimization

### Loading Strategies
- **Eager**: Load immediately (default)
- **Lazy**: Load when container becomes visible
- **Preload**: Preload script but defer execution

### Bundle Optimization
- Code splitting support
- Tree shaking compatibility
- Asset optimization
- Compression support

### Memory Management
- Automatic cleanup on unmount
- Memory leak detection
- Garbage collection optimization
- Resource pooling

## Migration Guide

### From v0.2.x to v0.3.x

1. **Event Bus Changes**:
   ```typescript
   // Old
   eventBus.emit('event', { data: 'value' });
   
   // New
   eventBus.emit('event', { data: 'value' }, { source: 'my-app' });
   ```

2. **Loading Function Signature**:
   ```typescript
   // Old
   loadMicrofrontend(config, options);
   
   // New
   loadMicrofrontend(config, options, hooks);
   ```

3. **Performance Monitoring**:
   ```typescript
   // Enable in options
   loadMicrofrontend(config, { enablePerformanceMonitoring: true });
   ```

### Current Version: v0.3.2

Latest stable release with enhanced performance monitoring, improved TypeScript support, and extended framework compatibility.

## 🔗 Related Packages

- **[@re-shell/cli](https://github.com/Re-Shell/reshell-monorepo/tree/main/packages/cli)** - Command-line interface for creating and managing Re-Shell projects
- **[@re-shell/ui](https://github.com/Re-Shell/reshell-monorepo/tree/main/packages/ui)** - Comprehensive React component library for microfrontend applications

## 📖 Documentation

For comprehensive documentation, examples, and guides, visit:

- **[Core API Documentation](https://github.com/Re-Shell/reshell-monorepo/tree/main/packages/core/docs)**
- **[Getting Started Guide](https://github.com/Re-Shell/reshell-monorepo/blob/main/docs/getting-started.md)**
- **[Best Practices](https://github.com/Re-Shell/reshell-monorepo/blob/main/docs/best-practices.md)**
- **[Migration Guide](https://github.com/Re-Shell/reshell-monorepo/blob/main/docs/migration.md)**

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](https://github.com/Re-Shell/reshell-monorepo/blob/main/CONTRIBUTING.md) for details on:

- Code of Conduct
- Development setup
- Submitting pull requests
- Reporting issues

## 📄 License

MIT License - see [LICENSE](https://github.com/Re-Shell/reshell-monorepo/blob/main/LICENSE) for details.

## 📞 Support

- **🐛 Bug Reports**: [GitHub Issues](https://github.com/Re-Shell/reshell-monorepo/issues)
- **💬 Discussions**: [GitHub Discussions](https://github.com/Re-Shell/reshell-monorepo/discussions)
- **📚 Documentation**: [GitHub Wiki](https://github.com/Re-Shell/reshell-monorepo/wiki)
- **📧 Email**: [<EMAIL>](mailto:<EMAIL>)
