export interface ComponentData {
  id: string;
  name: string;
  description: string;
  category: string;
  props: Array<{
    name: string;
    type: string;
    default?: string;
    description: string;
    required?: boolean;
  }>;
  examples: Array<{
    name: string;
    code: string;
    description?: string;
  }>;
}

export const componentData: ComponentData[] = [
  {
    id: 'button',
    name: 'Button',
    description: 'A versatile button component with multiple variants, sizes, and states.',
    category: 'Actions',
    props: [
      {
        name: 'variant',
        type: '"primary" | "secondary" | "success" | "danger" | "warning" | "info" | "ghost" | "outline"',
        default: '"primary"',
        description: 'The visual style variant of the button',
      },
      {
        name: 'size',
        type: '"xs" | "small" | "medium" | "large" | "xl"',
        default: '"medium"',
        description: 'The size of the button',
      },
      {
        name: 'loading',
        type: 'boolean',
        default: 'false',
        description: 'Shows a loading spinner and disables the button',
      },
      {
        name: 'disabled',
        type: 'boolean',
        default: 'false',
        description: 'Disables the button interaction',
      },
      {
        name: 'fullWidth',
        type: 'boolean',
        default: 'false',
        description: 'Makes the button take full width of its container',
      },
      {
        name: 'children',
        type: 'ReactNode',
        description: 'The content to display inside the button',
        required: true,
      },
      {
        name: 'onClick',
        type: '() => void',
        description: 'Callback fired when the button is clicked',
      },
    ],
    examples: [
      {
        name: 'Basic Usage',
        code: `<Button variant="primary">
  Click me
</Button>`,
      },
      {
        name: 'All Variants',
        code: `<div className="flex flex-wrap gap-3">
  <Button variant="primary">Primary</Button>
  <Button variant="secondary">Secondary</Button>
  <Button variant="success">Success</Button>
  <Button variant="danger">Danger</Button>
  <Button variant="warning">Warning</Button>
  <Button variant="info">Info</Button>
  <Button variant="ghost">Ghost</Button>
  <Button variant="outline">Outline</Button>
</div>`,
      },
      {
        name: 'Different Sizes',
        code: `<div className="flex items-center gap-3">
  <Button size="xs">Extra Small</Button>
  <Button size="small">Small</Button>
  <Button size="medium">Medium</Button>
  <Button size="large">Large</Button>
  <Button size="xl">Extra Large</Button>
</div>`,
      },
      {
        name: 'States',
        code: `<div className="flex gap-3">
  <Button loading>Loading</Button>
  <Button disabled>Disabled</Button>
  <Button fullWidth>Full Width</Button>
</div>`,
      },
    ],
  },
  {
    id: 'input',
    name: 'Input',
    description: 'A flexible input component for forms with validation and different states.',
    category: 'Forms',
    props: [
      {
        name: 'label',
        type: 'string',
        description: 'The label text for the input',
      },
      {
        name: 'placeholder',
        type: 'string',
        description: 'Placeholder text shown when input is empty',
      },
      {
        name: 'type',
        type: '"text" | "email" | "password" | "number" | "tel" | "url"',
        default: '"text"',
        description: 'The input type',
      },
      {
        name: 'size',
        type: '"sm" | "md" | "lg"',
        default: '"md"',
        description: 'The size of the input',
      },
      {
        name: 'error',
        type: 'boolean',
        default: 'false',
        description: 'Whether the input is in an error state',
      },
      {
        name: 'disabled',
        type: 'boolean',
        default: 'false',
        description: 'Whether the input is disabled',
      },
      {
        name: 'fullWidth',
        type: 'boolean',
        default: 'false',
        description: 'Makes the input take full width',
      },
      {
        name: 'value',
        type: 'string',
        description: 'The controlled value of the input',
      },
      {
        name: 'onChange',
        type: '(event: ChangeEvent<HTMLInputElement>) => void',
        description: 'Callback fired when the input value changes',
      },
    ],
    examples: [
      {
        name: 'Basic Usage',
        code: `<Input
  label="Email"
  placeholder="Enter your email"
  type="email"
/>`,
      },
      {
        name: 'Different Sizes',
        code: `<div className="space-y-4">
  <Input label="Small" size="sm" placeholder="Small input" />
  <Input label="Medium" size="md" placeholder="Medium input" />
  <Input label="Large" size="lg" placeholder="Large input" />
</div>`,
      },
      {
        name: 'States',
        code: `<div className="space-y-4">
  <Input label="Normal" placeholder="Normal state" />
  <Input label="Error" error placeholder="Error state" />
  <Input label="Disabled" disabled placeholder="Disabled state" />
</div>`,
      },
    ],
  },
  {
    id: 'card',
    name: 'Card',
    description: 'A flexible container component for grouping related content.',
    category: 'Layout',
    props: [
      {
        name: 'variant',
        type: '"default" | "outlined" | "elevated"',
        default: '"default"',
        description: 'The visual style variant of the card',
      },
      {
        name: 'padding',
        type: '"none" | "sm" | "md" | "lg"',
        default: '"md"',
        description: 'The internal padding of the card',
      },
      {
        name: 'hover',
        type: 'boolean',
        default: 'false',
        description: 'Whether the card should have hover effects',
      },
      {
        name: 'children',
        type: 'ReactNode',
        description: 'The content to display inside the card',
        required: true,
      },
      {
        name: 'className',
        type: 'string',
        description: 'Additional CSS classes to apply',
      },
    ],
    examples: [
      {
        name: 'Basic Usage',
        code: `<Card>
  <div className="p-6">
    <h3 className="text-lg font-semibold">Card Title</h3>
    <p className="text-gray-600 mt-2">
      This is a basic card component.
    </p>
  </div>
</Card>`,
      },
      {
        name: 'Card Variants',
        code: `<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
  <Card variant="default">
    <div className="p-4">
      <h4 className="font-semibold">Default</h4>
      <p className="text-sm text-gray-600">Default card style</p>
    </div>
  </Card>
  
  <Card variant="outlined">
    <div className="p-4">
      <h4 className="font-semibold">Outlined</h4>
      <p className="text-sm text-gray-600">Card with border</p>
    </div>
  </Card>
  
  <Card variant="elevated">
    <div className="p-4">
      <h4 className="font-semibold">Elevated</h4>
      <p className="text-sm text-gray-600">Card with shadow</p>
    </div>
  </Card>
</div>`,
      },
    ],
  },
  {
    id: 'badge',
    name: 'Badge',
    description: 'A small status indicator or label component.',
    category: 'Feedback',
    props: [
      {
        name: 'variant',
        type: '"primary" | "secondary" | "success" | "warning" | "error"',
        default: '"primary"',
        description: 'The color variant of the badge',
      },
      {
        name: 'size',
        type: '"sm" | "md" | "lg"',
        default: '"md"',
        description: 'The size of the badge',
      },
      {
        name: 'children',
        type: 'ReactNode',
        description: 'The content to display inside the badge',
        required: true,
      },
    ],
    examples: [
      {
        name: 'Basic Usage',
        code: `<Badge variant="primary">New</Badge>`,
      },
      {
        name: 'All Variants',
        code: `<div className="flex flex-wrap gap-2">
  <Badge variant="primary">Primary</Badge>
  <Badge variant="secondary">Secondary</Badge>
  <Badge variant="success">Success</Badge>
  <Badge variant="warning">Warning</Badge>
  <Badge variant="error">Error</Badge>
</div>`,
      },
      {
        name: 'Different Sizes',
        code: `<div className="flex items-center gap-2">
  <Badge size="sm">Small</Badge>
  <Badge size="md">Medium</Badge>
  <Badge size="lg">Large</Badge>
</div>`,
      },
    ],
  },
  {
    id: 'modal',
    name: 'Modal',
    description: 'A modal dialog component for displaying content in an overlay.',
    category: 'Overlay',
    props: [
      {
        name: 'isOpen',
        type: 'boolean',
        description: 'Whether the modal is open',
        required: true,
      },
      {
        name: 'onClose',
        type: '() => void',
        description: 'Callback fired when the modal should close',
        required: true,
      },
      {
        name: 'title',
        type: 'string',
        description: 'The title of the modal',
      },
      {
        name: 'size',
        type: '"sm" | "md" | "lg" | "xl" | "full"',
        default: '"md"',
        description: 'The size of the modal',
      },
      {
        name: 'closeOnOverlayClick',
        type: 'boolean',
        default: 'true',
        description: 'Whether clicking the overlay should close the modal',
      },
      {
        name: 'children',
        type: 'ReactNode',
        description: 'The content to display inside the modal',
        required: true,
      },
    ],
    examples: [
      {
        name: 'Basic Usage',
        code: `const [isOpen, setIsOpen] = useState(false);

return (
  <>
    <Button onClick={() => setIsOpen(true)}>
      Open Modal
    </Button>
    
    <Modal
      isOpen={isOpen}
      onClose={() => setIsOpen(false)}
      title="Example Modal"
    >
      <p>This is the modal content.</p>
    </Modal>
  </>
);`,
      },
    ],
  },
];

export const categories = [
  'All',
  'Actions',
  'Forms',
  'Layout',
  'Feedback',
  'Overlay',
  'Navigation',
  'Data Display',
];

export const getComponentsByCategory = (category: string) => {
  if (category === 'All') return componentData;
  return componentData.filter(component => component.category === category);
};

export const searchComponents = (query: string) => {
  const lowercaseQuery = query.toLowerCase();
  return componentData.filter(component =>
    component.name.toLowerCase().includes(lowercaseQuery) ||
    component.description.toLowerCase().includes(lowercaseQuery) ||
    component.category.toLowerCase().includes(lowercaseQuery)
  );
};