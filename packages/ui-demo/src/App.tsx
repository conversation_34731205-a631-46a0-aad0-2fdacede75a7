import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  Card, 
  Badge, 
  Alert, 
  Input,
  Textarea,
  Checkbox,
  Radio,
  Select,
  Switch,
  Modal,
  Tabs,
  Progress,
  Spinner,
  Tooltip,
  Avatar,
  Icon,
  Divider,
  Breadcrumb,
  Pagination
} from '@re-shell/ui';

import ComponentPlayground from './components/ComponentPlayground';
import SearchBox from './components/SearchBox';
import CodeBlock from './components/CodeBlock';
import { componentData, categories, getComponentsByCategory, searchComponents } from './data/componentData';
import { theme, cssVariables } from './theme';

function App() {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [filteredComponents, setFilteredComponents] = useState(componentData);

  // Demo state for interactive components
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    newsletter: false,
    theme: 'light',
    notifications: true,
    message: '',
    country: '',
  });

  const [buttonConfig, setButtonConfig] = useState({
    variant: 'primary' as const,
    size: 'medium' as const,
    loading: false,
    disabled: false,
    fullWidth: false,
  });

  const [inputConfig, setInputConfig] = useState({
    size: 'md' as const,
    error: false,
    disabled: false,
    fullWidth: false,
  });

  // Apply theme CSS variables
  useEffect(() => {
    const root = document.documentElement;
    const variables = isDarkMode ? cssVariables.dark : cssVariables.light;
    
    Object.entries(variables).forEach(([key, value]) => {
      root.style.setProperty(key, value);
    });
    
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isDarkMode]);

  // Filter components based on category and search
  useEffect(() => {
    let filtered = getComponentsByCategory(selectedCategory);
    
    if (searchQuery) {
      filtered = searchComponents(searchQuery);
    }
    
    setFilteredComponents(filtered);
  }, [selectedCategory, searchQuery]);

  const generateButtonCode = () => {
    const props = [];
    if (buttonConfig.variant !== 'primary') props.push(`variant="${buttonConfig.variant}"`);
    if (buttonConfig.size !== 'medium') props.push(`size="${buttonConfig.size}"`);
    if (buttonConfig.loading) props.push('loading');
    if (buttonConfig.disabled) props.push('disabled');
    if (buttonConfig.fullWidth) props.push('fullWidth');
    
    const propsString = props.length > 0 ? ` ${props.join(' ')}` : '';
    return `<Button${propsString}>\n  Click me\n</Button>`;
  };

  const generateInputCode = () => {
    const props = [];
    props.push('label="Email"');
    props.push('placeholder="Enter your email"');
    if (inputConfig.size !== 'md') props.push(`size="${inputConfig.size}"`);
    if (inputConfig.error) props.push('error');
    if (inputConfig.disabled) props.push('disabled');
    if (inputConfig.fullWidth) props.push('fullWidth');
    
    const propsString = props.join('\n  ');
    return `<Input\n  ${propsString}\n/>`;
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDarkMode ? 'bg-gray-900 text-white' : 'bg-gradient-to-br from-purple-50 via-white to-purple-50 text-gray-900'
    }`}>
      {/* Header */}
      <header className={`border-b ${isDarkMode ? 'border-gray-800 bg-gray-900' : 'border-purple-100 bg-white/80'} backdrop-blur-sm sticky top-0 z-50`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-700 rounded-lg flex items-center justify-center">
                <Icon name="star" size="sm" className="text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-purple-800 bg-clip-text text-transparent">
                  Re-Shell UI
                </h1>
                <p className="text-xs text-gray-500">Component Library</p>
              </div>
            </div>

            {/* Search */}
            <div className="flex-1 max-w-lg mx-8">
              <SearchBox 
                onSearch={setSearchQuery}
                placeholder="Search components, props, examples..."
              />
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-4">
              <Switch
                checked={isDarkMode}
                onChange={(e) => setIsDarkMode(e.target.checked)}
                label=""
              />
              <span className="text-sm text-gray-500">
                {isDarkMode ? 'Dark' : 'Light'}
              </span>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open('https://github.com/your-org/re-shell', '_blank')}
              >
                <Icon name="star" size="sm" />
                GitHub
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <section className="text-center py-12 mb-12">
          <h2 className="text-4xl sm:text-5xl font-bold mb-4">
            <span className="bg-gradient-to-r from-purple-600 via-purple-700 to-purple-800 bg-clip-text text-transparent">
              Beautiful Components
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            A comprehensive React component library with a delightful developer experience. 
            Fully customizable, accessible, and built with modern design principles.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button size="large" onClick={() => setShowModal(true)}>
              <Icon name="play" size="sm" />
              Get Started
            </Button>
            <Button variant="outline" size="large">
              <Icon name="document" size="sm" />
              Documentation
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{componentData.length}+</div>
              <div className="text-sm text-gray-500">Components</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">100%</div>
              <div className="text-sm text-gray-500">TypeScript</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">A11y</div>
              <div className="text-sm text-gray-500">Accessible</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">0</div>
              <div className="text-sm text-gray-500">Dependencies</div>
            </div>
          </div>
        </section>

        {/* Quick Examples */}
        <section className="mb-12">
          <h3 className="text-2xl font-bold mb-6">Quick Examples</h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Button Playground */}
            <ComponentPlayground
              title="Button"
              description="Interactive button with configurable properties"
              component={
                <Button
                  variant={buttonConfig.variant}
                  size={buttonConfig.size}
                  loading={buttonConfig.loading}
                  disabled={buttonConfig.disabled}
                  fullWidth={buttonConfig.fullWidth}
                >
                  Click me
                </Button>
              }
              code={generateButtonCode()}
              controls={
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Variant</label>
                      <Select
                        value={buttonConfig.variant}
                        onChange={(e) => setButtonConfig(prev => ({ ...prev, variant: e.target.value as any }))}
                        options={[
                          { value: 'primary', label: 'Primary' },
                          { value: 'secondary', label: 'Secondary' },
                          { value: 'success', label: 'Success' },
                          { value: 'danger', label: 'Danger' },
                          { value: 'warning', label: 'Warning' },
                          { value: 'info', label: 'Info' },
                          { value: 'ghost', label: 'Ghost' },
                          { value: 'outline', label: 'Outline' },
                        ]}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Size</label>
                      <Select
                        value={buttonConfig.size}
                        onChange={(e) => setButtonConfig(prev => ({ ...prev, size: e.target.value as any }))}
                        options={[
                          { value: 'xs', label: 'Extra Small' },
                          { value: 'small', label: 'Small' },
                          { value: 'medium', label: 'Medium' },
                          { value: 'large', label: 'Large' },
                          { value: 'xl', label: 'Extra Large' },
                        ]}
                      />
                    </div>
                  </div>
                  <div className="flex gap-4">
                    <Checkbox
                      checked={buttonConfig.loading}
                      onChange={(e) => setButtonConfig(prev => ({ ...prev, loading: e.target.checked }))}
                      label="Loading"
                    />
                    <Checkbox
                      checked={buttonConfig.disabled}
                      onChange={(e) => setButtonConfig(prev => ({ ...prev, disabled: e.target.checked }))}
                      label="Disabled"
                    />
                    <Checkbox
                      checked={buttonConfig.fullWidth}
                      onChange={(e) => setButtonConfig(prev => ({ ...prev, fullWidth: e.target.checked }))}
                      label="Full Width"
                    />
                  </div>
                </div>
              }
            />

            {/* Input Playground */}
            <ComponentPlayground
              title="Input"
              description="Form input with validation states"
              component={
                <Input
                  label="Email"
                  placeholder="Enter your email"
                  size={inputConfig.size}
                  error={inputConfig.error}
                  disabled={inputConfig.disabled}
                  fullWidth={inputConfig.fullWidth}
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                />
              }
              code={generateInputCode()}
              controls={
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Size</label>
                    <Select
                      value={inputConfig.size}
                      onChange={(e) => setInputConfig(prev => ({ ...prev, size: e.target.value as any }))}
                      options={[
                        { value: 'sm', label: 'Small' },
                        { value: 'md', label: 'Medium' },
                        { value: 'lg', label: 'Large' },
                      ]}
                    />
                  </div>
                  <div className="flex gap-4">
                    <Checkbox
                      checked={inputConfig.error}
                      onChange={(e) => setInputConfig(prev => ({ ...prev, error: e.target.checked }))}
                      label="Error State"
                    />
                    <Checkbox
                      checked={inputConfig.disabled}
                      onChange={(e) => setInputConfig(prev => ({ ...prev, disabled: e.target.checked }))}
                      label="Disabled"
                    />
                    <Checkbox
                      checked={inputConfig.fullWidth}
                      onChange={(e) => setInputConfig(prev => ({ ...prev, fullWidth: e.target.checked }))}
                      label="Full Width"
                    />
                  </div>
                </div>
              }
            />
          </div>
        </section>

        {/* Category Filters */}
        <section className="mb-8">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category)}
              >
                {category}
                {category !== 'All' && (
                  <Badge variant="secondary" size="sm" className="ml-2">
                    {getComponentsByCategory(category).length}
                  </Badge>
                )}
              </Button>
            ))}
          </div>
        </section>

        {/* Component Grid */}
        <section className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredComponents.map((component) => (
            <Card 
              key={component.id} 
              variant="outlined" 
              className="hover:shadow-lg transition-shadow duration-200"
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold">{component.name}</h3>
                  <Badge variant="primary" size="sm">
                    {component.category}
                  </Badge>
                </div>
                <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                  {component.description}
                </p>
                
                <div className="space-y-3">
                  <div className="text-xs text-gray-500">
                    {component.props.length} props • {component.examples.length} examples
                  </div>
                  
                  {component.examples[0] && (
                    <CodeBlock
                      code={component.examples[0].code}
                      title="Basic Usage"
                      className="text-xs"
                    />
                  )}
                </div>
              </div>
            </Card>
          ))}
        </section>

        {/* No Results */}
        {filteredComponents.length === 0 && (
          <div className="text-center py-12">
            <Icon name="search" size="2xl" className="text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-500 mb-2">No components found</h3>
            <p className="text-gray-400">
              Try adjusting your search or category filter
            </p>
          </div>
        )}
      </div>

      {/* Getting Started Modal */}
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title="Get Started with Re-Shell UI"
        size="lg"
      >
        <div className="space-y-6">
          <div>
            <h4 className="font-semibold mb-2">Installation</h4>
            <CodeBlock
              code="npm install @re-shell/ui"
              language="bash"
              title="Package Manager"
            />
          </div>
          
          <div>
            <h4 className="font-semibold mb-2">Basic Usage</h4>
            <CodeBlock
              code={`import { Button, Card, Input } from '@re-shell/ui';

function MyComponent() {
  return (
    <Card>
      <div className="p-6">
        <Input label="Name" placeholder="Enter your name" />
        <Button className="mt-4">Submit</Button>
      </div>
    </Card>
  );
}`}
              title="React Component"
            />
          </div>
          
          <div className="flex gap-3">
            <Button variant="primary" fullWidth onClick={() => setShowModal(false)}>
              Start Building
            </Button>
            <Button variant="outline" fullWidth>
              View Docs
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}

export default App;