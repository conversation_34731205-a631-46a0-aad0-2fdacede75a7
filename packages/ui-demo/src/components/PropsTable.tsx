import React from 'react';
import { Badge } from '@re-shell/ui';

interface PropDefinition {
  name: string;
  type: string;
  default?: string;
  description: string;
  required?: boolean;
}

interface PropsTableProps {
  props: PropDefinition[];
}

export const PropsTable: React.FC<PropsTableProps> = ({ props }) => {
  return (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse">
        <thead>
          <tr className="border-b border-gray-200">
            <th className="text-left py-3 px-4 font-semibold text-gray-900">Name</th>
            <th className="text-left py-3 px-4 font-semibold text-gray-900">Type</th>
            <th className="text-left py-3 px-4 font-semibold text-gray-900">Default</th>
            <th className="text-left py-3 px-4 font-semibold text-gray-900">Description</th>
          </tr>
        </thead>
        <tbody>
          {props.map((prop) => (
            <tr key={prop.name} className="border-b border-gray-100 hover:bg-gray-50">
              <td className="py-3 px-4">
                <div className="flex items-center gap-2">
                  <code className="bg-gray-100 text-purple-600 px-2 py-1 rounded text-sm font-mono">
                    {prop.name}
                  </code>
                  {prop.required && (
                    <Badge variant="error" size="sm">
                      Required
                    </Badge>
                  )}
                </div>
              </td>
              <td className="py-3 px-4">
                <code className="bg-blue-50 text-blue-600 px-2 py-1 rounded text-sm font-mono">
                  {prop.type}
                </code>
              </td>
              <td className="py-3 px-4">
                {prop.default ? (
                  <code className="bg-green-50 text-green-600 px-2 py-1 rounded text-sm font-mono">
                    {prop.default}
                  </code>
                ) : (
                  <span className="text-gray-400">-</span>
                )}
              </td>
              <td className="py-3 px-4 text-gray-700">
                {prop.description}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default PropsTable;