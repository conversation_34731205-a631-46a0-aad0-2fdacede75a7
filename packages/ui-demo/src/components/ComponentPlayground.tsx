import React, { useState, ReactNode } from 'react';
import { <PERSON>, Button, Tabs, Switch, Divider } from '@re-shell/ui';
import CodeBlock from './CodeBlock';
import PropsTable from './PropsTable';

interface PlaygroundTab {
  key: string;
  label: string;
  content: ReactNode;
}

interface ComponentPlaygroundProps {
  title: string;
  description?: string;
  component: ReactNode;
  code: string;
  props?: Array<{
    name: string;
    type: string;
    default?: string;
    description: string;
    required?: boolean;
  }>;
  controls?: ReactNode;
  variants?: Array<{
    name: string;
    component: ReactNode;
    code: string;
  }>;
  className?: string;
}

export const ComponentPlayground: React.FC<ComponentPlaygroundProps> = ({
  title,
  description,
  component,
  code,
  props,
  controls,
  variants = [],
  className = '',
}) => {
  const [showCode, setShowCode] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [selectedVariant, setSelectedVariant] = useState(0);

  const tabs: PlaygroundTab[] = [
    {
      key: 'preview',
      label: 'Preview',
      content: (
        <div className="space-y-6">
          {/* Controls */}
          {controls && (
            <>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="text-sm font-semibold text-gray-700 mb-3">Controls</h4>
                {controls}
              </div>
              <Divider />
            </>
          )}

          {/* Variants */}
          {variants.length > 0 && (
            <>
              <div>
                <h4 className="text-sm font-semibold text-gray-700 mb-3">Variants</h4>
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant={selectedVariant === -1 ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedVariant(-1)}
                  >
                    Default
                  </Button>
                  {variants.map((variant, index) => (
                    <Button
                      key={variant.name}
                      variant={selectedVariant === index ? 'primary' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedVariant(index)}
                    >
                      {variant.name}
                    </Button>
                  ))}
                </div>
              </div>
              <Divider />
            </>
          )}

          {/* Preview Area */}
          <div
            className={`
              relative min-h-[200px] p-8 rounded-lg border-2 border-dashed border-gray-200
              ${darkMode ? 'bg-gray-900' : 'bg-white'}
              flex items-center justify-center
            `}
          >
            {/* Theme Toggle */}
            <div className="absolute top-3 right-3">
              <Switch
                checked={darkMode}
                onChange={(e) => setDarkMode(e.target.checked)}
                label=""
                size="sm"
              />
              <span className="text-xs text-gray-500 ml-2">
                {darkMode ? 'Dark' : 'Light'}
              </span>
            </div>

            {/* Component */}
            <div className={darkMode ? 'dark' : ''}>
              {selectedVariant >= 0 ? variants[selectedVariant].component : component}
            </div>
          </div>

          {/* Code Toggle */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowCode(!showCode)}
            >
              {showCode ? 'Hide Code' : 'Show Code'}
            </Button>
          </div>

          {/* Code Block */}
          {showCode && (
            <CodeBlock
              code={selectedVariant >= 0 ? variants[selectedVariant].code : code}
              title="React Component"
            />
          )}
        </div>
      ),
    },
  ];

  if (props && props.length > 0) {
    tabs.push({
      key: 'props',
      label: 'Props',
      content: <PropsTable props={props} />,
    });
  }

  return (
    <Card className={`${className}`}>
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
        {description && (
          <p className="text-gray-600">{description}</p>
        )}
      </div>

      <div className="p-6">
        <Tabs
          items={tabs}
          defaultActiveKey="preview"
          variant="line"
          size="md"
        />
      </div>
    </Card>
  );
};

export default ComponentPlayground;