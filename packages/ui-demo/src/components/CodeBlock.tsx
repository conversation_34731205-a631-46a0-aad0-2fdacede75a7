import React, { useState } from 'react';
import { Button, Icon } from '@re-shell/ui';

interface CodeBlockProps {
  code: string;
  language?: string;
  title?: string;
  showCopy?: boolean;
  className?: string;
}

export const CodeBlock: React.FC<CodeBlockProps> = ({
  code,
  language = 'tsx',
  title,
  showCopy = true,
  className = '',
}) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  return (
    <div className={`border border-gray-200 rounded-lg overflow-hidden ${className}`}>
      {title && (
        <div className="bg-gray-50 px-4 py-2 border-b border-gray-200 flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">{title}</span>
          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded uppercase">
            {language}
          </span>
        </div>
      )}
      <div className="relative">
        <pre className="bg-gray-900 text-gray-100 p-4 overflow-x-auto text-sm">
          <code className={`language-${language}`}>{code}</code>
        </pre>
        {showCopy && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopy}
            className="absolute top-2 right-2 text-gray-400 hover:text-white"
            aria-label={copied ? 'Copied!' : 'Copy code'}
          >
            {copied ? (
              <Icon name="check" size="sm" />
            ) : (
              <Icon name="document" size="sm" />
            )}
          </Button>
        )}
      </div>
    </div>
  );
};

export default CodeBlock;