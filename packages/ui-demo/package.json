{"name": "@re-shell/ui-demo", "version": "1.0.0", "private": true, "description": "Demo application for testing Re-Shell UI components", "scripts": {"dev": "vite --port 3333", "build": "vite build", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@re-shell/ui": "workspace:*"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.0", "typescript": "^5.0.0", "vite": "^4.0.0"}}