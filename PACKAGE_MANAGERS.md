# Package Manager Compatibility Guide

Re-Shell supports multiple package managers to accommodate different team preferences and workflows.

## Supported Package Managers

- **pnpm** (Primary, recommended)
- **npm**
- **yarn**
- **bun**

## Using pnpm (Recommended)

```bash
# Install dependencies
pnpm install

# Build all packages
pnpm build

# Start development
pnpm dev

# Run tests
pnpm test

# Lint code
pnpm lint

# Clean build artifacts
pnpm clean
```

## Using npm

```bash
# Install dependencies
npm install

# Build all packages
npm run build:npm

# Start development
npm run dev:npm

# Run tests
npm test

# Lint code
npm run lint

# Clean build artifacts
npm run clean
```

## Using Yarn

```bash
# Install dependencies
yarn install

# Build all packages
yarn build:yarn

# Start development
yarn dev:yarn

# Run tests
yarn test

# Lint code
yarn lint

# Clean build artifacts
yarn clean
```

## Using Bun

```bash
# Install dependencies
bun install

# Build all packages
bun run build

# Start development
bun run dev

# Run tests
bun run test

# Lint code
bun run lint

# Clean build artifacts
bun run clean
```

## Package Manager Configuration

### pnpm

The monorepo is configured with pnpm workspaces in `pnpm-workspace.yaml`:

```yaml
packages:
  - 'apps/*'
  - 'packages/*'
```

### npm

npm workspaces are configured in `package.json`:

```json
"workspaces": [
  "apps/*",
  "packages/*"
]
```

### Yarn

Yarn workspaces are also configured in `package.json`:

```json
"workspaces": [
  "apps/*",
  "packages/*"
]
```

### Bun

Bun uses the npm workspace configuration from `package.json`.

## Switching Between Package Managers

We've included a helper script to make it easier to switch between package managers:

```bash
# Switch to pnpm
./manager.sh pnpm

# Switch to npm
./manager.sh npm

# Switch to yarn
./manager.sh yarn

# Switch to bun
./manager.sh bun
```

This script updates the lock files and ensures that the right scripts are available.

## Recommendations

- **For New Projects**: Use pnpm for its efficiency and strict node_modules structure
- **For Existing npm Projects**: Continue with npm for compatibility
- **For Performance**: Try bun for the fastest installation and build times
- **For Plugin Ecosystem**: Use yarn if you rely on yarn-specific plugins

## Troubleshooting

If you encounter issues with a specific package manager:

1. Clean all node_modules folders:
   ```bash
   find . -name "node_modules" -type d -exec rm -rf {} +
   ```
2. Delete all lock files:
   ```bash
   rm -f yarn.lock package-lock.json pnpm-lock.yaml bun.lock
   ```
3. Try installing with a different package manager

## CI/CD Considerations

When setting up CI/CD pipelines, use `manager.sh` to ensure the correct package manager is used:

```yaml
steps:
  - name: Setup
    run: ./manager.sh pnpm
  - name: Install
    run: pnpm install
  - name: Build
    run: pnpm build
```