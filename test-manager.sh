#!/bin/bash

# test-manager.sh - Script to test monorepo with different package managers

PACKAGE_MANAGER=$1

if [ -z "$PACKAGE_MANAGER" ]; then
  echo "Usage: ./test-manager.sh <package-manager>"
  echo "Available package managers: pnpm, npm, yarn, bun"
  exit 1
fi

echo "Testing with $PACKAGE_MANAGER..."

# Clean environment
rm -rf node_modules
find . -name "node_modules" -type d -exec rm -rf {} +
find . -name "dist" -type d -exec rm -rf {} +

case $PACKAGE_MANAGER in
  pnpm)
    echo "Testing PNPM installation..."
    pnpm install
    INSTALL_STATUS=$?

    if [ $INSTALL_STATUS -eq 0 ]; then
      echo "✅ PNPM install successful"
      echo "Testing PNPM build..."
      pnpm build
      BUILD_STATUS=$?

      if [ $BUILD_STATUS -eq 0 ]; then
        echo "✅ PNPM build successful"
      else
        echo "❌ PNPM build failed"
      fi
    else
      echo "❌ PNPM install failed"
    fi
    ;;

  npm)
    echo "Testing NPM installation..."
    cp -f npm-package.json package.json.backup
    cp -f package.json package.json.original
    cp -f npm-package.json package.json

    npm install
    INSTALL_STATUS=$?

    if [ $INSTALL_STATUS -eq 0 ]; then
      echo "✅ NPM install successful"
      echo "Testing NPM build..."
      npm run build
      BUILD_STATUS=$?

      if [ $BUILD_STATUS -eq 0 ]; then
        echo "✅ NPM build successful"
      else
        echo "❌ NPM build failed"
      fi
    else
      echo "❌ NPM install failed"
    fi

    # Restore original package.json
    mv -f package.json.original package.json
    ;;

  yarn)
    echo "Testing Yarn installation..."
    yarn install
    INSTALL_STATUS=$?

    if [ $INSTALL_STATUS -eq 0 ]; then
      echo "✅ Yarn install successful"
      echo "Testing Yarn build..."
      yarn build:yarn
      BUILD_STATUS=$?

      if [ $BUILD_STATUS -eq 0 ]; then
        echo "✅ Yarn build successful"
      else
        echo "❌ Yarn build failed"
      fi
    else
      echo "❌ Yarn install failed"
    fi
    ;;

  bun)
    echo "Testing Bun installation..."
    bun install
    INSTALL_STATUS=$?

    if [ $INSTALL_STATUS -eq 0 ]; then
      echo "✅ Bun install successful"
      echo "Testing Bun build..."
      bun run build
      BUILD_STATUS=$?

      if [ $BUILD_STATUS -eq 0 ]; then
        echo "✅ Bun build successful"
      else
        echo "❌ Bun build failed"
      fi
    else
      echo "❌ Bun install failed"
    fi
    ;;

  *)
    echo "Unknown package manager: $PACKAGE_MANAGER"
    echo "Available package managers: pnpm, npm, yarn, bun"
    exit 1
    ;;
esac