# ReShell

A lightweight microfrontend shell for React applications.

## Overview

ReShell is a framework that enables multiple teams to work on separate repositories while combining their work into a cohesive application. It provides tools and patterns for loading, communicating between, and managing microfrontends in a React application.

## Project Structure

```
reshell/
├── packages/
│   ├── reshell-core/     # Core framework
│   ├── reshell-cli/      # CLI tool
│   └── demo/             # Demo application
├── docs/
│   ├── guides/           # User guides
│   ├── api/              # API documentation
│   ├── architecture/     # Architecture documentation
│   └── planning/         # Roadmaps and task lists
├── .cursor/
│   └── rules/            # Cursor AI rules
├── .github/
│   └── workflows/        # CI/CD workflows
```

## Features

- **Lightweight**: Minimal overhead for loading microfrontends
- **Flexible**: Support for different microfrontend patterns
- **Dev Experience**: CLI tools for scaffolding new microfrontends
- **Communication**: Event bus for inter-microfrontend communication
- **Routing**: Built-in routing support
- **Type Safety**: Full TypeScript support

## Getting Started

### Prerequisites

- Node.js (v16 or later)
- pnpm (recommended) or npm

### Installation

```bash
# Clone the repository
git clone https://github.com/Re-Shell/reshell.git
cd reshell

# Install dependencies
pnpm install

# Build packages
pnpm build

# Run demo
pnpm dev
```

### Creating a Microfrontend

Use the ReShell CLI to create a new microfrontend:

```bash
# Install CLI globally
npm install -g @re-shell/cli

# Create a new microfrontend
reshell create-mf my-microfrontend --team MyTeam
```

## Documentation

- [Getting Started Guide](docs/guides/getting-started.md)
- [Configuration Guide](docs/guides/configuration.md)
- [Architecture Documentation](docs/architecture/architecture.md)
- [Project Roadmap](docs/planning/ROADMAP.md)
- [Task List](docs/planning/TODO.md)

## Contributing

Please read our [Commit Convention](COMMIT_CONVENTION.md) for details on our code of conduct and the process for submitting pull requests.

## License

[MIT License](LICENSE)