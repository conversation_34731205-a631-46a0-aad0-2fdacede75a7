# Commit Message Convention

## Overview

ReShell follows [Conventional Commits](https://www.conventionalcommits.org/) specification for commit messages. This leads to more readable messages that are easy to follow when looking through the project history and helps with automatic versioning and changelog generation.

## Format

Each commit message consists of a **header**, a **body** and a **footer**. The header has a special format that includes a **type**, a **scope** and a **subject**:

```
<type>(<scope>): <subject>
<BLANK LINE>
<body>
<BLANK LINE>
<footer>
```

The **header** is mandatory and the **scope** of the header is optional.

### Type

Must be one of the following:

- **feat**: A new feature
- **fix**: A bug fix
- **docs**: Documentation only changes
- **style**: Changes that do not affect the meaning of the code (white-space, formatting, etc)
- **refactor**: A code change that neither fixes a bug nor adds a feature
- **perf**: A code change that improves performance
- **test**: Adding missing tests or correcting existing tests
- **build**: Changes that affect the build system or external dependencies
- **ci**: Changes to our CI configuration files and scripts
- **chore**: Other changes that don't modify src or test files
- **revert**: Reverts a previous commit

### Scope

The scope should be the name of the module affected (as perceived by the person reading the changelog generated from commit messages).

The following is the list of supported scopes:

- **core**: Core shell functionality
- **router**: Routing system
- **events**: Event bus and communication
- **loader**: Microfrontend loading mechanism
- **plugins**: Plugin system
- **react**: React integration
- **vue**: Vue integration
- **angular**: Angular integration
- **svelte**: Svelte integration
- **solid**: SolidJS integration
- **security**: Security-related changes
- **config**: Configuration-related changes
- **cli**: CLI tool
- **demo**: Demo applications
- **rules**: Rule definitions
- **docs**: Documentation
- **devtools**: Developer tools

### Subject

The subject contains a succinct description of the change:

- use the imperative, present tense: "change" not "changed" nor "changes"
- don't capitalize the first letter
- no dot (.) at the end

### Body

The body should include the motivation for the change and contrast this with previous behavior.

### Footer

The footer should contain any information about **Breaking Changes** and is also the place to reference GitHub issues that this commit **Closes**.

Breaking Changes should start with the word `BREAKING CHANGE:` with a space or two newlines. The rest of the commit message is then used for this.

## Examples

### Feature with scope

```
feat(router): add support for nested routes

Implement nested routing capability to allow microfrontends to define 
child routes that maintain the parent context.

Closes #123
```

### Bug fix with breaking change

```
fix(core): prevent memory leak in event listeners

The event listeners were not properly cleaned up when a microfrontend was 
unmounted, causing a memory leak in long-running applications.

BREAKING CHANGE: EventBus API now requires explicit subscription disposal
```

### Documentation update

```
docs: update README with new API documentation

Expand the API documentation section with more detailed examples 
and update the installation instructions to cover the new CLI tool.
```

### Multiple scopes

If the changes affect multiple scopes, you can comma-separate them:

```
refactor(core,router): simplify route registration API

Simplify the route registration API to make it more intuitive and 
reduce boilerplate code when registering new routes.
```

### Chore with scope

```
chore(build): update dependency versions

Update all dependencies to their latest versions to benefit from
bug fixes and performance improvements.
```

## Tooling

To enforce this convention, the project uses:

- [commitlint](https://commitlint.js.org/) to lint commit messages
- [husky](https://typicode.github.io/husky/) to run commitlint as a pre-commit hook

Please make sure to install these development dependencies and configure git hooks when setting up your development environment.

## Relationship to Semantic Versioning

The commit type can be used to automatically determine the next semantic version bump:

- `fix` type commits trigger a PATCH release (1.0.0 → 1.0.1)
- `feat` type commits trigger a MINOR release (1.0.0 → 1.1.0)
- Commits with `BREAKING CHANGE` in the footer trigger a MAJOR release (1.0.0 → 2.0.0)

This enables automated changelog generation and version management.

