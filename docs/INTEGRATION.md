# Integrating Microfrontends with Re-Shell

This guide explains how to integrate a newly created microfrontend into your Re-Shell application.

## Prerequisites

- A shell application using Re-Shell
- A microfrontend created with the Re-Shell CLI (`re-shell add`)

## Integration Steps

### 1. Build the Microfrontend

First, build your microfrontend to generate the bundle:

```bash
cd my-microfrontend
npm install
npm run build
```

This will create a UMD bundle in the `dist` directory, typically named `mf.umd.js`.

### 2. Configure the Shell Application

In your shell application's configuration, add the microfrontend:

```tsx
// In your shell application's App.tsx or similar file
import { ShellProvider, MicrofrontendContainer, MicrofrontendConfig } from '@re-shell/core';

// Add your new microfrontend to the configuration
const MICROFRONTENDS: MicrofrontendConfig[] = [
  // ... existing microfrontends
  {
    id: 'my-microfrontend',
    name: 'My Microfrontend',
    url: '/apps/my-microfrontend/dist/mf.umd.js', // Path to the built bundle
    containerId: 'my-microfrontend-container',
    route: '/my-feature', // The route path for this microfrontend
    team: 'My Team'
  }
];

function App() {
  return (
    <ShellProvider initialMicrofrontends={MICROFRONTENDS}>
      {/* ... other content */}
      
      {/* Add a container for the microfrontend */}
      <div id="my-microfrontend-container">
        <MicrofrontendContainer
          config={MICROFRONTENDS.find(mf => mf.id === 'my-microfrontend')}
        />
      </div>
    </ShellProvider>
  );
}
```

### 3. Add Routing (Optional)

If you want to enable routing for your microfrontend, add a route configuration:

```tsx
import { Route, Routes } from 'react-router-dom';

function App() {
  return (
    <ShellProvider initialMicrofrontends={MICROFRONTENDS}>
      <Routes>
        {/* ... existing routes */}
        <Route 
          path="/my-feature/*" 
          element={
            <div id="my-microfrontend-container">
              <MicrofrontendContainer
                config={MICROFRONTENDS.find(mf => mf.id === 'my-microfrontend')}
              />
            </div>
          } 
        />
      </Routes>
    </ShellProvider>
  );
}
```

### 4. Configure Asset Hosting

Ensure your microfrontend's bundle is accessible from your shell application. Common approaches include:

- **Monorepo Setup**: In a monorepo, you can reference the bundles directly from the build output
- **Separate Deployments**: Host each microfrontend on its own URL/domain
- **CDN Deployment**: Upload bundles to a CDN and reference them

### 5. Event Communication

Microfrontends can communicate using the Re-Shell event bus:

```tsx
// In your microfrontend
import { eventBus } from '@re-shell/core';

// Emit an event
eventBus.emit('user:selected', { userId: 123 });

// Listen for events
eventBus.on('app:ready', () => {
  console.log('Shell application is ready');
});
```

### 6. Development Workflow

For development, you can:

1. Run each microfrontend in isolation:
   ```bash
   cd my-microfrontend
   npm run dev
   ```

2. Run the shell application which loads all microfrontends:
   ```bash
   cd shell
   npm run dev
   ```

## Deployment Considerations

### 1. Versioning

Each microfrontend should be versioned independently. Use semantic versioning to ensure compatibility.

### 2. Deployment Pipelines

Set up separate deployment pipelines for:
- Shell application
- Each microfrontend
- Shared libraries

### 3. Runtime Integration

Decide on your runtime integration strategy:
- **Build-time integration**: Include microfrontends at build time
- **Runtime integration**: Load microfrontends dynamically at runtime (recommended)

## Best Practices

1. **Shared Dependencies**: Manage shared dependencies carefully to avoid duplicate libraries
2. **Styling**: Use CSS modules or CSS-in-JS to avoid style conflicts
3. **Contracts**: Define clear contracts between microfrontends and the shell
4. **Testing**: Test microfrontends both in isolation and integrated with the shell

## Common Issues

1. **CORS Issues**: Ensure proper CORS headers are set when loading microfrontends from different origins
2. **Loading Order**: Consider the order of loading microfrontends if they depend on each other
3. **Shared State**: Be careful with shared state to avoid tight coupling between microfrontends