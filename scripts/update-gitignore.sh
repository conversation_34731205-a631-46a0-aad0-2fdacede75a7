#!/bin/bash

# Script to update .gitignore files across all submodules
# Usage: ./scripts/update-gitignore.sh

# Make sure we're in the root directory
cd "$(dirname "$0")/.." || exit 1

# Check if the template file exists
if [ ! -f ".gitignore-submodule-template" ]; then
  echo "Error: .gitignore-submodule-template not found in the root directory."
  exit 1
fi

# Function to update .gitignore in a directory
update_gitignore() {
  local dir=$1
  if [ -d "$dir" ]; then
    echo "Updating .gitignore in $dir"
    cp .gitignore-submodule-template "$dir/.gitignore"
  fi
}

# Update .gitignore in all packages
for pkg in packages/*; do
  if [ -d "$pkg" ]; then
    update_gitignore "$pkg"
  fi
done

# Update .gitignore in all apps
for app in apps/*; do
  if [ -d "$app" ]; then
    update_gitignore "$app"
  fi
done

echo "All .gitignore files have been updated!"
