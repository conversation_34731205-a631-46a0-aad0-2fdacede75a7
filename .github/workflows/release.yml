name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., 1.0.0)'
        required: true
        type: string

permissions:
  contents: write
  packages: write
  actions: read

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          registry-url: 'https://registry.npmjs.org'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run tests
        run: pnpm run test

      - name: Build packages
        run: pnpm run build

      - name: Generate changelog
        id: changelog
        run: |
          if [ -f CHANGELOG.md ]; then
            # Extract changelog for current version
            VERSION=${GITHUB_REF#refs/tags/v}
            CHANGELOG=$(awk "/^## \[?$VERSION\]?/{flag=1; next} /^## \[?[0-9]/{flag=0} flag" CHANGELOG.md)
            echo "changelog<<EOF" >> $GITHUB_OUTPUT
            echo "$CHANGELOG" >> $GITHUB_OUTPUT
            echo "EOF" >> $GITHUB_OUTPUT
          else
            echo "changelog=No changelog available" >> $GITHUB_OUTPUT
          fi

      - name: Create GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          body: |
            ## Changes
            ${{ steps.changelog.outputs.changelog }}
            
            ## Assets
            - **CLI**: Install with `npm install -g @re-shell/cli@${{ github.ref_name }}`
            - **Core**: Install with `npm install @re-shell/core@${{ github.ref_name }}`
            - **UI Components**: Install with `npm install @re-shell/ui@${{ github.ref_name }}`
          draft: false
          prerelease: ${{ contains(github.ref, 'alpha') || contains(github.ref, 'beta') || contains(github.ref, 'rc') }}

      - name: Publish to NPM
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: |
          # Set up npm authentication
          echo "//registry.npmjs.org/:_authToken=${NODE_AUTH_TOKEN}" > ~/.npmrc
          
          # Publish packages that have changed
          pnpm publish -r --access public --no-git-checks
          
          # Clean up
          rm ~/.npmrc

      - name: Upload CLI package
        uses: actions/upload-artifact@v3
        with:
          name: re-shell-cli-${{ github.ref_name }}
          path: packages/cli/dist/

      - name: Upload Core package
        uses: actions/upload-artifact@v3
        with:
          name: re-shell-core-${{ github.ref_name }}
          path: packages/core/dist/

      - name: Upload UI package
        uses: actions/upload-artifact@v3
        with:
          name: re-shell-ui-${{ github.ref_name }}
          path: packages/ui/dist/