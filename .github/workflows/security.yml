name: Security

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run security checks daily at 2 AM UTC
    - cron: '0 2 * * *'

permissions:
  contents: read
  security-events: write
  actions: read

jobs:
  dependency-check:
    name: Dependency Security Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run security audit
        run: |
          # Run npm audit for each package
          echo "🔍 Running security audit..."
          pnpm audit --audit-level moderate

      - name: Check for known vulnerabilities
        uses: actions/dependency-review-action@v3
        if: github.event_name == 'pull_request'

  codeql:
    name: CodeQL Analysis
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: [ 'javascript', 'typescript' ]

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: ${{ matrix.language }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: false

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build packages
        run: pnpm run build

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2

  secrets-scan:
    name: Secrets Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run TruffleHog OSS
        uses: trufflesecurity/trufflehog@v3.54.0
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified

  license-check:
    name: License Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: false

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Install license checker
        run: npm install -g license-checker

      - name: Check licenses
        run: |
          echo "📄 Checking package licenses..."
          cd packages/cli && license-checker --summary
          cd ../core && license-checker --summary
          cd ../ui && license-checker --summary
          cd ../ui-demo && license-checker --summary

      - name: Check for prohibited licenses
        run: |
          echo "🚫 Checking for prohibited licenses..."
          PROHIBITED_LICENSES="GPL-2.0,GPL-3.0,AGPL-1.0,AGPL-3.0,LGPL-2.0,LGPL-2.1,LGPL-3.0"
          cd packages/cli && license-checker --excludeLicenses "$PROHIBITED_LICENSES"
          cd ../core && license-checker --excludeLicenses "$PROHIBITED_LICENSES"
          cd ../ui && license-checker --excludeLicenses "$PROHIBITED_LICENSES"
          cd ../ui-demo && license-checker --excludeLicenses "$PROHIBITED_LICENSES"