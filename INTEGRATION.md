# Re-Shell Integration Guide

This document explains how to integrate microfrontends using the Re-Shell framework.

## Monorepo Structure

The Re-Shell monorepo contains:

1. **Core Framework** (`packages/core`) - Core microfrontend framework
2. **UI Library** (`packages/ui`) - Shared UI components  
3. **CLI Tools** (`packages/cli`) - Tools for creating and managing microfrontends

## Integration Architecture

### Component Interaction

1. **Core Framework (`@re-shell/core`)**
   - Provides the core functionality for loading and managing microfrontends
   - Defines the contract for communication between microfrontends
   - Supplies React components for seamless integration

2. **UI Library (`@re-shell/ui`)**
   - Shared UI components for consistent design across microfrontends
   - Reusable components like buttons, modals, forms

3. **CLI Tools (`@re-shell/cli`)**
   - Command-line interface for creating and managing microfrontends
   - Scaffolding tools for new microfrontend projects
   - Build and deployment utilities

## Integration Contract

All microfrontends must adhere to the following contract:

```typescript
interface MicrofrontendInterface {
  // Mount the microfrontend to a DOM container
  mount: (containerId: string) => void;

  // Unmount and clean up resources
  unmount: (containerId: string) => void;
}

// Global exports
declare global {
  interface Window {
    [key: string]: MicrofrontendInterface;
  }
}
```

## Development Workflow

1. **Local Development**
   - Run each microfrontend in development mode on a different port
   - Use the standalone demo with proxying to load microfrontends

2. **Deployment**
   - Build each microfrontend as a UMD bundle
   - Deploy each microfrontend to its own location (CDN or server)
   - Configure the shell application to load microfrontends from their respective locations

## Communication Between Microfrontends

Microfrontends can communicate using:

1. **Event Bus** - For broadcasting events that multiple microfrontends might be interested in
2. **Custom Events** - For direct communication between specific microfrontends
3. **Shared State** - For data that needs to be accessed by multiple microfrontends

## Getting Started

### Prerequisites

- Node.js (v16 or later)
- pnpm (recommended), npm, yarn, or bun

### Installation

1. Clone the monorepo:
   ```bash
   git clone https://github.com/Re-Shell/re-shell.git
   cd re-shell
   ```

2. Install dependencies:
   ```bash
   pnpm install
   ```

3. Build packages:
   ```bash
   pnpm build
   ```

4. Start development (if applicable):
   ```bash
   pnpm dev
   ```

## Adding New Microfrontends

### Using the CLI

Install the CLI globally:
```bash
npm install -g @re-shell/cli
```

Create a new microfrontend:
```bash
re-shell add my-microfrontend --team MyTeam
```

### CLI Commands

The Re-Shell CLI supports the following commands:

- `add` - Add a new microfrontend to your project
- `remove` - Remove a microfrontend from your project
- `list` - List all registered microfrontends
- `build` - Build one or all microfrontends
- `serve` - Serve microfrontends locally for development

### CLI Options

- `--team <team>` - Team name (ownership)
- `--template <template>` - Template to use (react, svelte, vue)
- `--route <route>` - Route path for the microfrontend
- `--port <port>` - Dev server port

## Integration Testing

Test your microfrontend integrations by:

1. Building all packages: `pnpm build`
2. Running tests: `pnpm test`
3. Testing CLI functionality: `pnpm --filter @re-shell/cli test`

## Package Manager Support

This monorepo supports multiple package managers:
- **pnpm** (recommended)
- **npm**
- **yarn** 
- **bun**

See [PACKAGE_MANAGERS.md](./PACKAGE_MANAGERS.md) for detailed usage instructions.

## Versioning

- All packages use semantic versioning
- Packages are versioned independently
- Use peer dependencies for core framework compatibility