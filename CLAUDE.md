# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build & Test Commands
- Build all: `pnpm run build`
- Dev mode (all): `pnpm run dev`
- Lint all: `pnpm run lint`
- Lint specific app: `pnpm --filter @re-shell/<app-name> lint`
- Test all: `pnpm run test`
- Clean build artifacts: `pnpm run clean`

## Code Style Guidelines
- **TypeScript**: Strict mode enabled with noImplicitAny and strictNullChecks
- **Formatting**:
  - Use 2 spaces for indentation
  - Max line length: 100 characters
  - Single quotes for strings
  - Semicolons required
- **React**: Use functional components with hooks
- **Imports**:
  - Group imports: external, then internal, then relative
  - Import from workspace packages using: `@re-shell/*`
- **Naming**:
  - Components: PascalCase
  - Functions/variables: camelCase
  - Files: kebab-case for components, camelCase for utilities
- **Error Handling**: Use try/catch for async operations, prefer early returns