#!/bin/bash

# test-install.sh - Script to test installation with different package managers

test_pnpm() {
  echo "Testing PNPM installation..."
  rm -rf node_modules
  find . -name "node_modules" -type d -exec rm -rf {} +
  pnpm install
  if [ $? -eq 0 ]; then
    echo "✅ PNPM install successful"
  else
    echo "❌ PNPM install failed"
  fi
}

test_npm() {
  echo "Testing NPM installation..."
  rm -rf node_modules
  find . -name "node_modules" -type d -exec rm -rf {} +
  cp -f package.json package.json.original
  cp -f npm-package.json package.json
  npm install
  if [ $? -eq 0 ]; then
    echo "✅ NPM install successful"
  else
    echo "❌ NPM install failed"
  fi
  mv -f package.json.original package.json
}

test_yarn() {
  echo "Testing Yarn installation..."
  rm -rf node_modules
  find . -name "node_modules" -type d -exec rm -rf {} +
  yarn install
  if [ $? -eq 0 ]; then
    echo "✅ Yarn install successful"
  else
    echo "❌ Yarn install failed"
  fi
}

test_bun() {
  echo "Testing Bun installation..."
  rm -rf node_modules
  find . -name "node_modules" -type d -exec rm -rf {} +
  bun install
  if [ $? -eq 0 ]; then
    echo "✅ Bun install successful"
  else
    echo "❌ Bun install failed"
  fi
}

# Run tests based on arguments
if [ -z "$1" ]; then
  echo "Testing all package managers..."
  test_pnpm
  test_npm
  test_yarn
  test_bun
else
  case $1 in
    pnpm)
      test_pnpm
      ;;
    npm)
      test_npm
      ;;
    yarn)
      test_yarn
      ;;
    bun)
      test_bun
      ;;
    *)
      echo "Unknown package manager: $1"
      echo "Available package managers: pnpm, npm, yarn, bun"
      exit 1
      ;;
  esac
fi