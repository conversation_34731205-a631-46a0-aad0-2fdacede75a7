# Dependencies
node_modules
.pnp
.pnp.js
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# Build outputs
dist
dist-ssr
*.local
build
lib
storybook-static

# Testing
coverage
.nyc_output
test-output
test-results
test-reports
.vitest-cache

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/launch.json
!.vscode/tasks.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.history
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production
.env.*.local
.env.development.local
.env.test.local
.env.production.local

# Cache
.cache
.parcel-cache
.npm
.eslintcache
.stylelintcache
.rollup.cache
.babel-cache
.next/cache
.swc/

# Package managers
.pnpm-store/
.pnpm-debug.log
.npmrc
.yarnrc
.yarnrc.yml
bun.lock
package-lock.json
pnpm-lock.yaml
npm-package.json
.gitignore-submodule-template

# Turbo
.turbo

# Temporary files
.tmp
.temp
tmp/
temp/
*.tmp
*.temp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# AI tools
**/.claude/settings.local.json
.cursor/
.copilot/

# CLI output
test-cli/
test-cli-*/
test-output/

# Misc
.vercel
.netlify
.docusaurus
.serverless/
.fusebox/
.dynamodb/
.tern-port
.vscode-test
.webpack/
out/
