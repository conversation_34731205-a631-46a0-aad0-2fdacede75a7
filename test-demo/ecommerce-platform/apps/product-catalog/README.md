# Product Catalog Microfrontend

A comprehensive product catalog microfrontend built with React and TypeScript for the e-commerce platform.

## Features

- **Product Grid Display**: Responsive grid layout showing products with images, pricing, and ratings
- **Advanced Search**: Real-time search functionality across product names
- **Category Filtering**: Filter products by categories (Electronics, Clothing, Home, Fitness)
- **Sorting Options**: Sort by name, price (low to high, high to low), and rating
- **Add to Cart**: Seamless cart integration with event-driven communication
- **Stock Management**: Visual indicators for stock levels and out-of-stock items
- **Responsive Design**: Mobile-first responsive layout
- **Loading States**: Smooth loading experience with spinners
- **Notifications**: Success notifications when items are added to cart

## Technology Stack

- **React 18.2.0**: Modern React with hooks
- **Vite**: Fast build tool and development server
- **TypeScript**: Type-safe development
- **CSS3**: Modern styling with gradients and animations
- **Event Bus**: Cross-microfrontend communication

## Development

```bash
# Install dependencies
pnpm install

# Start development server (port 3001)
pnpm run dev

# Build for production
pnpm run build

# Preview production build
pnpm run preview

# Type checking
pnpm run type-check

# Linting
pnpm run lint
```

## Microfrontend Integration

This app can be integrated as a microfrontend:

```javascript
// Mount the microfrontend
const unmount = window.ProductCatalog.mount(document.getElementById('product-catalog-container'));

// Unmount when needed
unmount();
```

## Event Communication

The app emits the following events:

- `cart:add`: When a product is added to cart
  ```javascript
  {
    payload: { id, name, price, ... },
    source: 'product-catalog'
  }
  ```

## Port Configuration

- **Development**: http://localhost:3001
- **Production**: Configured for microfrontend deployment

## Architecture

- **Component-based**: Modular React components
- **State Management**: Local React state with hooks
- **Event-driven**: Cross-app communication via event bus
- **Responsive**: Mobile-first design approach

## Product Data Structure

```javascript
{
  id: number,
  name: string,
  price: number,
  category: string,
  image: string,
  stock: number,
  rating: number
}
```