import React, { useState, useEffect } from 'react';
import './App.css';

function App() {
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [loading, setLoading] = useState(true);
  const [cart, setCart] = useState([]);

  // Mock product data
  const mockProducts = [
    { id: 1, name: 'MacBook Pro', price: 2499, category: 'electronics', image: 'https://via.placeholder.com/300x200', stock: 15, rating: 4.8 },
    { id: 2, name: 'iPhone 15', price: 999, category: 'electronics', image: 'https://via.placeholder.com/300x200', stock: 25, rating: 4.7 },
    { id: 3, name: 'Nike Air Max', price: 120, category: 'clothing', image: 'https://via.placeholder.com/300x200', stock: 50, rating: 4.5 },
    { id: 4, name: 'Coffee Maker', price: 89, category: 'home', image: 'https://via.placeholder.com/300x200', stock: 30, rating: 4.3 },
    { id: 5, name: 'Yoga Mat', price: 35, category: 'fitness', image: 'https://via.placeholder.com/300x200', stock: 40, rating: 4.4 },
    { id: 6, name: 'Wireless Headphones', price: 199, category: 'electronics', image: 'https://via.placeholder.com/300x200', stock: 20, rating: 4.6 },
    { id: 7, name: 'Leather Jacket', price: 299, category: 'clothing', image: 'https://via.placeholder.com/300x200', stock: 8, rating: 4.7 },
    { id: 8, name: 'Smart Watch', price: 349, category: 'electronics', image: 'https://via.placeholder.com/300x200', stock: 12, rating: 4.5 },
    { id: 9, name: 'Running Shoes', price: 140, category: 'fitness', image: 'https://via.placeholder.com/300x200', stock: 35, rating: 4.6 },
    { id: 10, name: 'Kitchen Blender', price: 75, category: 'home', image: 'https://via.placeholder.com/300x200', stock: 22, rating: 4.2 },
    { id: 11, name: 'Gaming Mouse', price: 79, category: 'electronics', image: 'https://via.placeholder.com/300x200', stock: 18, rating: 4.4 },
    { id: 12, name: 'Denim Jeans', price: 85, category: 'clothing', image: 'https://via.placeholder.com/300x200', stock: 45, rating: 4.3 }
  ];

  const categories = ['all', 'electronics', 'clothing', 'home', 'fitness'];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setProducts(mockProducts);
      setFilteredProducts(mockProducts);
      setLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    let filtered = products;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    // Sort products
    filtered = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        case 'rating':
          return b.rating - a.rating;
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });

    setFilteredProducts(filtered);
  }, [products, searchTerm, selectedCategory, sortBy]);

  const addToCart = (product) => {
    // Emit event for microfrontend communication
    if (window.eventBus) {
      window.eventBus.emit('cart:add', {
        payload: product,
        source: 'product-catalog'
      });
    }

    // Update local cart state
    setCart(prevCart => {
      const existingItem = prevCart.find(item => item.id === product.id);
      if (existingItem) {
        return prevCart.map(item =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      }
      return [...prevCart, { ...product, quantity: 1 }];
    });

    // Show success notification
    showNotification(`${product.name} added to cart!`);
  };

  const showNotification = (message) => {
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 3000);
  };

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<span key={i} className="star filled">★</span>);
    }

    if (hasHalfStar) {
      stars.push(<span key="half" className="star half">★</span>);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<span key={`empty-${i}`} className="star">☆</span>);
    }

    return stars;
  };

  if (loading) {
    return (
      <div className="app">
        <div className="loading">
          <div className="spinner"></div>
          <p>Loading products...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="app">
      <header className="header">
        <h1>Product Catalog</h1>
        <div className="cart-info">
          Cart Items: {cart.reduce((sum, item) => sum + item.quantity, 0)}
        </div>
      </header>

      <div className="filters">
        <div className="search-bar">
          <input
            type="text"
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filter-controls">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="category-select"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
              </option>
            ))}
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="sort-select"
          >
            <option value="name">Name A-Z</option>
            <option value="price-low">Price: Low to High</option>
            <option value="price-high">Price: High to Low</option>
            <option value="rating">Highest Rated</option>
          </select>
        </div>
      </div>

      <div className="results-info">
        Showing {filteredProducts.length} of {products.length} products
      </div>

      <div className="product-grid">
        {filteredProducts.map(product => (
          <div key={product.id} className="product-card">
            <img src={product.image} alt={product.name} className="product-image" />
            <div className="product-info">
              <h3 className="product-name">{product.name}</h3>
              <div className="product-rating">
                {renderStars(product.rating)}
                <span className="rating-text">({product.rating})</span>
              </div>
              <p className="product-price">${product.price}</p>
              <div className="product-meta">
                <span className={`stock ${product.stock < 10 ? 'low' : ''}`}>
                  Stock: {product.stock}
                </span>
                <span className="category">{product.category}</span>
              </div>
              <button
                onClick={() => addToCart(product)}
                className="add-to-cart-btn"
                disabled={product.stock === 0}
              >
                {product.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
              </button>
            </div>
          </div>
        ))}
      </div>

      {filteredProducts.length === 0 && (
        <div className="no-results">
          <h2>No products found</h2>
          <p>Try adjusting your search terms or filters.</p>
        </div>
      )}
    </div>
  );
}

export default App;