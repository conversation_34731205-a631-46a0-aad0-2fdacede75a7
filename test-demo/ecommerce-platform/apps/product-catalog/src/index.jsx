import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.jsx';

// Microfrontend mounting logic
const mount = (element) => {
  const root = ReactDOM.createRoot(element);
  root.render(<App />);
  
  return () => {
    root.unmount();
  };
};

// If running in standalone mode, mount to DOM
if (process.env.NODE_ENV === 'development' && !window.productCatalogMounted) {
  const rootElement = document.getElementById('root');
  if (rootElement) {
    mount(rootElement);
    window.productCatalogMounted = true;
  }
}

// Export for use as microfrontend
if (typeof window !== 'undefined') {
  window.ProductCatalog = { mount };
}

export { mount };