{"name": "shopping-cart", "version": "0.1.0", "description": "Shopping cart microfrontend for ecommerce platform", "private": true, "type": "module", "scripts": {"dev": "vite --port 3002 --host", "build": "tsc && vite build", "preview": "vite preview --port 3002", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"react": "18.2.0", "react-dom": "18.2.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8"}}