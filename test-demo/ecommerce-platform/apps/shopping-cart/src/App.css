/* Shopping Cart Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.header h1 {
  color: #333;
  font-size: 2rem;
  font-weight: 700;
}

.cart-summary {
  background: #4f46e5;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
}

.empty-cart {
  text-align: center;
  color: white;
  padding: 80px 20px;
}

.empty-cart-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.empty-cart h2 {
  font-size: 2rem;
  margin-bottom: 10px;
}

.empty-cart p {
  font-size: 1.1rem;
  opacity: 0.8;
}

.cart-content {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 30px;
}

.cart-items {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.cart-item {
  display: grid;
  grid-template-columns: 80px 1fr auto auto auto;
  gap: 20px;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #f3f4f6;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  background: #f3f4f6;
}

.item-details h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.item-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: #059669;
  margin-bottom: 3px;
}

.item-category {
  font-size: 0.9rem;
  color: #6b7280;
  text-transform: capitalize;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #f9fafb;
  border-radius: 8px;
  padding: 5px;
}

.quantity-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #4f46e5;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.quantity-btn:hover:not(:disabled) {
  background: #4338ca;
}

.quantity-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.quantity {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
}

.item-total {
  font-size: 1.2rem;
  font-weight: 700;
  color: #059669;
}

.remove-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.remove-btn:hover {
  background: #dc2626;
}

.cart-sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.promo-section,
.cart-totals,
.cart-actions {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.promo-section h3,
.cart-totals h3 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1.1rem;
}

.promo-input {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.promo-input input {
  flex: 1;
  padding: 10px;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
}

.promo-input input:focus {
  outline: none;
  border-color: #4f46e5;
}

.apply-btn {
  padding: 10px 20px;
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.apply-btn:hover {
  background: #4338ca;
}

.applied-promo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #dcfce7;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  color: #166534;
}

.remove-promo {
  background: none;
  border: none;
  color: #166534;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.total-row:last-child {
  border-bottom: none;
}

.total-row.discount {
  color: #059669;
}

.total-row.total {
  font-size: 1.2rem;
  font-weight: 700;
  color: #333;
  padding-top: 15px;
  border-top: 2px solid #e5e7eb;
}

.cart-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.clear-cart-btn,
.checkout-btn {
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.clear-cart-btn {
  background: #f3f4f6;
  color: #6b7280;
}

.clear-cart-btn:hover {
  background: #e5e7eb;
}

.checkout-btn {
  background: #059669;
  color: white;
}

.checkout-btn:hover {
  background: #047857;
}

/* Checkout Styles */
.checkout-container {
  max-width: 1200px;
  margin: 0 auto;
}

.checkout-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
}

.back-btn {
  background: white;
  border: none;
  padding: 10px 15px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  color: #4f46e5;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.checkout-header h1 {
  color: white;
  font-size: 2rem;
}

.checkout-content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 30px;
}

.checkout-form {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.form-section {
  margin-bottom: 30px;
}

.form-section h3 {
  margin-bottom: 20px;
  color: #333;
  font-size: 1.2rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;
}

.checkout-form input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  margin-bottom: 15px;
  transition: border-color 0.3s ease;
}

.checkout-form input:focus {
  outline: none;
  border-color: #4f46e5;
}

.order-summary {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f3f4f6;
  font-size: 14px;
}

.summary-totals {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 2px solid #e5e7eb;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.summary-row.total {
  font-size: 1.2rem;
  font-weight: 700;
  color: #333;
  padding-top: 15px;
  border-top: 2px solid #e5e7eb;
}

.place-order-btn {
  width: 100%;
  padding: 15px;
  background: #059669;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 20px;
  transition: background-color 0.3s ease;
}

.place-order-btn:hover:not(:disabled) {
  background: #047857;
}

.place-order-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Confirmation Styles */
.confirmation-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 70vh;
}

.confirmation-card {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 500px;
}

.success-icon {
  width: 80px;
  height: 80px;
  background: #10b981;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  margin: 0 auto 20px;
}

.confirmation-card h1 {
  color: #333;
  margin-bottom: 10px;
}

.confirmation-card p {
  color: #6b7280;
  margin-bottom: 30px;
}

.order-details,
.order-items {
  text-align: left;
  margin-bottom: 30px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 8px;
}

.order-details h3,
.order-items h3 {
  margin-bottom: 15px;
  color: #333;
}

.confirmation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
}

.confirmation-item:last-child {
  border-bottom: none;
}

.continue-shopping-btn {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.continue-shopping-btn:hover {
  background: #4338ca;
}

.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  animation: slideIn 0.3s ease;
  color: white;
  font-weight: 600;
}

.notification.success {
  background: #10b981;
}

.notification.error {
  background: #ef4444;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 10px;
  }

  .header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .cart-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .cart-item {
    grid-template-columns: 60px 1fr auto;
    gap: 15px;
  }

  .item-total,
  .remove-btn {
    grid-column: span 3;
    justify-self: end;
  }

  .checkout-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .header h1 {
    font-size: 1.5rem;
  }
}