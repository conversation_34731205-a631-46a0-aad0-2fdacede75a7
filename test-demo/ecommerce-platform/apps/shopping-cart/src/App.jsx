import React, { useState, useEffect } from 'react';
import './App.css';

function App() {
  const [cartItems, setCartItems] = useState([]);
  const [total, setTotal] = useState(0);
  const [tax, setTax] = useState(0);
  const [shipping, setShipping] = useState(0);
  const [discount, setDiscount] = useState(0);
  const [promoCode, setPromoCode] = useState('');
  const [checkoutStep, setCheckoutStep] = useState('cart'); // cart, checkout, confirmation
  const [loading, setLoading] = useState(false);
  const [orderConfirmation, setOrderConfirmation] = useState(null);

  // Promo codes
  const promoCodes = {
    'SAVE10': { discount: 0.1, description: '10% off' },
    'WELCOME': { discount: 0.15, description: '15% off for new customers' },
    'FREESHIP': { discount: 0, freeShipping: true, description: 'Free shipping' }
  };

  useEffect(() => {
    // Listen for cart events from other microfrontends
    if (window.eventBus) {
      const unsubscribe = window.eventBus.on('cart:add', (data) => {
        const product = data.payload;
        addItemToCart(product);
      });

      return () => window.eventBus.off(unsubscribe);
    }
  }, []);

  useEffect(() => {
    calculateTotals();
  }, [cartItems, discount]);

  const addItemToCart = (product) => {
    setCartItems(prev => {
      const existingItem = prev.find(item => item.id === product.id);
      if (existingItem) {
        return prev.map(item =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      }
      return [...prev, { ...product, quantity: 1 }];
    });
  };

  const updateQuantity = (id, newQuantity) => {
    if (newQuantity === 0) {
      removeItem(id);
      return;
    }

    setCartItems(prev =>
      prev.map(item =>
        item.id === id ? { ...item, quantity: Math.max(1, newQuantity) } : item
      )
    );
  };

  const removeItem = (id) => {
    setCartItems(prev => prev.filter(item => item.id !== id));
    showNotification('Item removed from cart');
  };

  const clearCart = () => {
    setCartItems([]);
    setPromoCode('');
    setDiscount(0);
    showNotification('Cart cleared');
  };

  const calculateTotals = () => {
    const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const discountAmount = subtotal * discount;
    const taxAmount = (subtotal - discountAmount) * 0.08; // 8% tax
    const shippingCost = subtotal > 50 || (promoCodes[promoCode]?.freeShipping) ? 0 : 10;
    
    setTotal(subtotal - discountAmount + taxAmount + shippingCost);
    setTax(taxAmount);
    setShipping(shippingCost);
  };

  const applyPromoCode = () => {
    const code = promoCodes[promoCode.toUpperCase()];
    if (code) {
      setDiscount(code.discount);
      showNotification(`Promo code applied: ${code.description}`);
    } else {
      setDiscount(0);
      showNotification('Invalid promo code', 'error');
    }
  };

  const removePromoCode = () => {
    setPromoCode('');
    setDiscount(0);
    showNotification('Promo code removed');
  };

  const proceedToCheckout = () => {
    if (cartItems.length === 0) {
      showNotification('Your cart is empty', 'error');
      return;
    }
    setCheckoutStep('checkout');
  };

  const completeCheckout = () => {
    setLoading(true);
    
    // Simulate payment processing
    setTimeout(() => {
      const orderId = 'ORD-' + Date.now();
      setOrderConfirmation({
        orderId,
        items: [...cartItems],
        total,
        timestamp: new Date().toLocaleString()
      });
      
      // Clear cart and reset state
      setCartItems([]);
      setPromoCode('');
      setDiscount(0);
      setCheckoutStep('confirmation');
      setLoading(false);
      
      // Emit checkout complete event
      if (window.eventBus) {
        window.eventBus.emit('checkout:complete', {
          payload: { orderId, total },
          source: 'shopping-cart'
        });
      }
    }, 2000);
  };

  const showNotification = (message, type = 'success') => {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 3000);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (checkoutStep === 'confirmation') {
    return (
      <div className="app">
        <div className="confirmation-container">
          <div className="confirmation-card">
            <div className="success-icon">✓</div>
            <h1>Order Confirmed!</h1>
            <p>Thank you for your purchase. Your order has been successfully processed.</p>
            
            <div className="order-details">
              <h3>Order Details</h3>
              <p><strong>Order ID:</strong> {orderConfirmation.orderId}</p>
              <p><strong>Total:</strong> {formatCurrency(orderConfirmation.total)}</p>
              <p><strong>Date:</strong> {orderConfirmation.timestamp}</p>
            </div>

            <div className="order-items">
              <h3>Items Ordered</h3>
              {orderConfirmation.items.map(item => (
                <div key={item.id} className="confirmation-item">
                  <span>{item.name} × {item.quantity}</span>
                  <span>{formatCurrency(item.price * item.quantity)}</span>
                </div>
              ))}
            </div>

            <button onClick={() => setCheckoutStep('cart')} className="continue-shopping-btn">
              Continue Shopping
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (checkoutStep === 'checkout') {
    return (
      <div className="app">
        <div className="checkout-container">
          <div className="checkout-header">
            <button onClick={() => setCheckoutStep('cart')} className="back-btn">
              ← Back to Cart
            </button>
            <h1>Checkout</h1>
          </div>

          <div className="checkout-content">
            <div className="checkout-form">
              <div className="form-section">
                <h3>Shipping Information</h3>
                <div className="form-row">
                  <input type="text" placeholder="First Name" required />
                  <input type="text" placeholder="Last Name" required />
                </div>
                <input type="email" placeholder="Email Address" required />
                <input type="text" placeholder="Street Address" required />
                <div className="form-row">
                  <input type="text" placeholder="City" required />
                  <input type="text" placeholder="State" required />
                  <input type="text" placeholder="ZIP Code" required />
                </div>
              </div>

              <div className="form-section">
                <h3>Payment Information</h3>
                <input type="text" placeholder="Card Number" required />
                <div className="form-row">
                  <input type="text" placeholder="MM/YY" required />
                  <input type="text" placeholder="CVV" required />
                </div>
                <input type="text" placeholder="Cardholder Name" required />
              </div>
            </div>

            <div className="order-summary">
              <h3>Order Summary</h3>
              {cartItems.map(item => (
                <div key={item.id} className="summary-item">
                  <span>{item.name} × {item.quantity}</span>
                  <span>{formatCurrency(item.price * item.quantity)}</span>
                </div>
              ))}
              
              <div className="summary-totals">
                <div className="summary-row">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0))}</span>
                </div>
                {discount > 0 && (
                  <div className="summary-row discount">
                    <span>Discount:</span>
                    <span>-{formatCurrency(cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0) * discount)}</span>
                  </div>
                )}
                <div className="summary-row">
                  <span>Tax:</span>
                  <span>{formatCurrency(tax)}</span>
                </div>
                <div className="summary-row">
                  <span>Shipping:</span>
                  <span>{shipping === 0 ? 'FREE' : formatCurrency(shipping)}</span>
                </div>
                <div className="summary-row total">
                  <span>Total:</span>
                  <span>{formatCurrency(total)}</span>
                </div>
              </div>

              <button 
                onClick={completeCheckout} 
                className="place-order-btn"
                disabled={loading}
              >
                {loading ? 'Processing...' : `Place Order - ${formatCurrency(total)}`}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="app">
      <header className="header">
        <h1>Shopping Cart</h1>
        <div className="cart-summary">
          {cartItems.length} {cartItems.length === 1 ? 'item' : 'items'} - {formatCurrency(total)}
        </div>
      </header>

      {cartItems.length === 0 ? (
        <div className="empty-cart">
          <div className="empty-cart-icon">🛒</div>
          <h2>Your cart is empty</h2>
          <p>Add some products to get started!</p>
        </div>
      ) : (
        <div className="cart-content">
          <div className="cart-items">
            {cartItems.map(item => (
              <div key={item.id} className="cart-item">
                <img src={item.image || 'https://via.placeholder.com/80x80'} alt={item.name} className="item-image" />
                
                <div className="item-details">
                  <h3 className="item-name">{item.name}</h3>
                  <p className="item-price">{formatCurrency(item.price)}</p>
                  <p className="item-category">{item.category}</p>
                </div>

                <div className="quantity-controls">
                  <button 
                    onClick={() => updateQuantity(item.id, item.quantity - 1)}
                    className="quantity-btn"
                    disabled={item.quantity <= 1}
                  >
                    -
                  </button>
                  <span className="quantity">{item.quantity}</span>
                  <button 
                    onClick={() => updateQuantity(item.id, item.quantity + 1)}
                    className="quantity-btn"
                  >
                    +
                  </button>
                </div>

                <div className="item-total">
                  {formatCurrency(item.price * item.quantity)}
                </div>

                <button 
                  onClick={() => removeItem(item.id)}
                  className="remove-btn"
                  title="Remove item"
                >
                  ×
                </button>
              </div>
            ))}
          </div>

          <div className="cart-sidebar">
            <div className="promo-section">
              <h3>Promo Code</h3>
              <div className="promo-input">
                <input
                  type="text"
                  placeholder="Enter promo code"
                  value={promoCode}
                  onChange={(e) => setPromoCode(e.target.value)}
                />
                <button onClick={applyPromoCode} className="apply-btn">
                  Apply
                </button>
              </div>
              {discount > 0 && (
                <div className="applied-promo">
                  <span>Applied: {promoCode}</span>
                  <button onClick={removePromoCode} className="remove-promo">×</button>
                </div>
              )}
            </div>

            <div className="cart-totals">
              <div className="total-row">
                <span>Subtotal:</span>
                <span>{formatCurrency(cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0))}</span>
              </div>
              {discount > 0 && (
                <div className="total-row discount">
                  <span>Discount ({Math.round(discount * 100)}%):</span>
                  <span>-{formatCurrency(cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0) * discount)}</span>
                </div>
              )}
              <div className="total-row">
                <span>Tax:</span>
                <span>{formatCurrency(tax)}</span>
              </div>
              <div className="total-row">
                <span>Shipping:</span>
                <span>{shipping === 0 ? 'FREE' : formatCurrency(shipping)}</span>
              </div>
              <div className="total-row total">
                <span>Total:</span>
                <span>{formatCurrency(total)}</span>
              </div>
            </div>

            <div className="cart-actions">
              <button onClick={clearCart} className="clear-cart-btn">
                Clear Cart
              </button>
              <button onClick={proceedToCheckout} className="checkout-btn">
                Proceed to Checkout
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;