<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Shopping Cart - E-commerce Platform</title>
  <meta name="description" content="Manage your shopping cart with advanced quantity controls, promo codes, and seamless checkout experience." />
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/src/index.jsx"></script>
  
  <!-- Microfrontend Event Bus -->
  <script>
    // Simple event bus for microfrontend communication
    window.eventBus = {
      events: {},
      emit(event, data) {
        if (this.events[event]) {
          this.events[event].forEach(callback => callback(data));
        }
        console.log('Event emitted:', event, data);
      },
      on(event, callback) {
        if (!this.events[event]) {
          this.events[event] = [];
        }
        this.events[event].push(callback);
        return () => {
          this.events[event] = this.events[event].filter(cb => cb !== callback);
        };
      },
      off(unsubscribe) {
        if (typeof unsubscribe === 'function') {
          unsubscribe();
        }
      }
    };
  </script>
</body>
</html>