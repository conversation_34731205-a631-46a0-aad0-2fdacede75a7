{"name": "ecommerce-platform", "version": "0.1.0", "description": "ecommerce-platform - A multi-framework monorepo", "private": true, "workspaces": ["apps/*", "packages/*", "libs/*", "tools/*"], "scripts": {"dev": "pnpm run --parallel -r dev", "build": "pnpm run --parallel -r build", "lint": "pnpm run --parallel -r lint", "test": "pnpm run --parallel -r test", "clean": "pnpm run --parallel -r clean", "type-check": "pnpm run --parallel -r type-check", "workspace:list": "re-shell workspace list", "workspace:graph": "re-shell workspace graph", "workspace:update": "re-shell workspace update"}, "devDependencies": {"@re-shell/cli": "^0.2.0"}, "engines": {"node": ">=16.0.0"}}